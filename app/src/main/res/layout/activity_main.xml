<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:viewBindingIgnore="true">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_Ip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="3dp"
        android:hint="Ip server"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edt_ip_server"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="***************" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:id="@+id/ll_amount_orderId"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="3dp"
        android:orientation="horizontal"
        android:weightSum="3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_Ip">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Amount">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:text="200" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:layout_width="3dp"
            android:layout_height="1dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="OrderID">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_orderID"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="orderID1" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:layout_width="3dp"
            android:layout_height="1dp"/>

        <com.google.android.material.textfield.TextInputLayout
                android:visibility="gone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="DepositID">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_depositID"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="deposit1" />

        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <HorizontalScrollView
        android:id="@+id/layout_transID"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="3dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_amount_orderId">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.textfield.TextInputLayout
                    android:visibility="gone"
                android:id="@+id/layout_amount_deposit"
                android:layout_width="130dp"
                android:layout_height="60dp"
                android:hint="Amount Deposit">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_amount_deposit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:text="200" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="200dp"
                android:layout_height="fill_parent"
                android:hint="TransID - void">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_transID"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="2023" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:hint="Phone">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:text="0332193377" />

            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </HorizontalScrollView>

    <HorizontalScrollView
            android:visibility="gone"
        android:id="@+id/ll_moto"
        android:layout_marginStart="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_transID"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:orientation="horizontal">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="100dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="Moto ID">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_moto_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:text="order1" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="190dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="Pan moto">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_pan_moto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:text="123" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="100dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="Exp date">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_exp_date_moto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="date"
                    android:text="12/25" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="100dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="CVV">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_cvv_moto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:text="123" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="200dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="CardHolderName">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_cardHolderName_moto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:text="XUAN THUAN" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="200dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:hint="OTA">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_ota_moto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:text="BOOKING" />
            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>
    </HorizontalScrollView>

    <HorizontalScrollView
        android:id="@+id/ll_options"
        android:layout_marginStart="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_moto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:orientation="horizontal">

            <LinearLayout
                android:paddingHorizontal="10dp"
                android:paddingVertical="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="#EEFACA"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:textAlignment="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tự động đóng \nMàn hình thành công (s)" />

                <Spinner
                    android:id="@+id/spnAutoDismissDlg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:prompt="@string/AutoDismissDlg"></Spinner>
            </LinearLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <LinearLayout
                android:paddingHorizontal="10dp"
                android:paddingVertical="10dp"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:layout_gravity="center"
                android:background="#EEFACA"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hiển thị thông báo\nconfirm Void" />

                <Spinner
                    android:id="@+id/spnConfirmVoid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:prompt="@string/AutoDismissDlg"></Spinner>
            </LinearLayout>

            <View
                android:layout_width="3dp"
                android:layout_height="1dp" />

            <LinearLayout
                android:paddingHorizontal="10dp"
                android:paddingVertical="10dp"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:layout_gravity="center"
                android:background="#EEFACA"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="192dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="Cho phép In \nhóa đơn khi void"
                    android:textAlignment="center" />

                <Spinner
                    android:id="@+id/spnPermitPrintReceipt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:prompt="@string/AutoDismissDlg"></Spinner>
            </LinearLayout>

        </LinearLayout>
    </HorizontalScrollView>

    <HorizontalScrollView
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_options">

        <LinearLayout
            android:layout_marginStart="5dp"
            android:layout_marginTop="5dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/payCard"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:onClick="onClick"
                    android:text="Quẹt thẻ" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_qrVaQr"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:onClick="onClick"
                    android:text="QR VaQR" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                        android:visibility="gone"
                    android:id="@+id/btn_qrVNpay"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="QR VNPAY" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                        android:visibility="gone"
                    android:id="@+id/btn_qrMOMO"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="QR Momo" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                        android:visibility="gone"
                    android:id="@+id/btn_qrZalo"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="QR Zalo" />

            </LinearLayout>

            <LinearLayout android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_deposit"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Deposit" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_setFinalAmount_deposit"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Set Final Amount"/>

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_finish_deposit"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Finish Deposit"/>

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_moto"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Moto" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_moto_deposit"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Moto-Deposit" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/clear_log"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Clear log" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/cancel_trans"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Cancel Trans" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/btn_void_trans"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Void Trans"
                    android:visibility="visible" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/check_server"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="get IP" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/get_info_device"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Get Info Device" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/get_status_battery"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Get Battery" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="1dp" />

                <Button
                    android:id="@+id/get_status_internet"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:text="Get Internet status" />

            </LinearLayout>

        </LinearLayout>

    </HorizontalScrollView>

    <ScrollView
        android:id="@+id/scrollView2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/txt_log"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:padding="10dp"
            android:text="log" />
    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>