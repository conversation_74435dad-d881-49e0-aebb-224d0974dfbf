<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".screen.HomeActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <Button
            android:id="@+id/btn_go_socket"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginEnd="5dp"
            android:text="Socket" />

        <Button
                android:visibility="gone"
            android:id="@+id/btn_go_pushPayment"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:text="PushPayment" />

        <Button
                android:visibility="visible"
            android:id="@+id/btn_go_Takashimaya"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="5dp"
            android:text="Takashimaya" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>