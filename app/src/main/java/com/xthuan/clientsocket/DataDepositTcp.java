package com.xthuan.clientsocket;

import com.google.gson.annotations.Expose;

public class DataDepositTcp {
    @Expose
    public String serviceName;
    @Expose
    public String orderId;
    @Expose
    public long totalAmount;
    @Expose
    public long finalAmount;
    @Expose
    public int autoDismissDlgTimer;
    @Expose
    public String description;
    @Expose
    public String depositID;
    @Expose
    public String wfId;
    @Expose
    public String phone;

    public DataDepositTcp() {
    }

    // object add deposit
    public DataDepositTcp(String serviceName, String orderId, long totalAmount, String depositID, String phone) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.totalAmount = totalAmount;
        this.depositID = depositID;
        this.phone = phone;
    }

    public DataDepositTcp(String serviceName, long totalAmount, String depositID) {
        this.serviceName = serviceName;
        this.totalAmount = totalAmount;
        this.depositID = depositID;
    }

    public DataDepositTcp(String serviceName, long totalAmount, long finalAmount, String depositID, String wfId) {
        this.serviceName = serviceName;
        this.totalAmount = totalAmount;
        this.finalAmount = finalAmount;
        this.depositID = depositID;
        this.wfId = wfId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getDepositID() {
        return depositID;
    }

    public void setDepositID(String depositID) {
        this.depositID = depositID;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public int getAutoDismissDlgTimer() {
        return autoDismissDlgTimer;
    }

    public void setAutoDismissDlgTimer(int autoDismissDlgTimer) {
        this.autoDismissDlgTimer = autoDismissDlgTimer;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public long getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(long finalAmount) {
        this.finalAmount = finalAmount;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }
}
