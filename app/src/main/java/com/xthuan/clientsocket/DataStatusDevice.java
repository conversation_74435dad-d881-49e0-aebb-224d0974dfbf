package com.xthuan.clientsocket;

import com.google.gson.annotations.Expose;

public class DataStatusDevice {
    @Expose
    public String serviceName;

    @Expose
    public String method;

    BatteryStatus batteryStatus;

    public DataStatusDevice(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public BatteryStatus getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(BatteryStatus batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public static class BatteryStatus {
        @Expose
        public float batteryPct;

        @Expose
        public boolean isCharging;

        public BatteryStatus() {
        }

        public BatteryStatus(float batteryPct, boolean isCharging) {
            this.batteryPct = batteryPct;
            this.isCharging = isCharging;
        }

        public float getBatteryPct() {
            return batteryPct;
        }

        public void setBatteryPct(float batteryPct) {
            this.batteryPct = batteryPct;
        }

        public boolean isCharging() {
            return isCharging;
        }

        public void setCharging(boolean charging) {
            isCharging = charging;
        }

    }
}
