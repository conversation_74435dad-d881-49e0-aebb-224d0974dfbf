package com.xthuan.clientsocket;

import android.content.Context;
import android.util.Log;

//import com.xthuan.clientsocket.Mpos.SaveLogController;
import com.xthuan.clientsocket.screen.MainActivity;

import java.io.BufferedInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SocketClient {
    private static final String TAG = SocketClient.class.getSimpleName();
    /** Service Name **/
    public static final String ADD_ORDER_INFOR = "ADD_ORDER_INFOR";
    public static final String CANCEL_ORDER = "CANCEL_ORDER";
    public static final String UPDATE_TRANSACTION = "UPDATE_TRANSACTION";
    public static final String serviceVoidTrans = "VOID_TRANSACTION";
    public static final String ADD_DEPOSIT = "ADD_DEPOSIT";
    public static final String SET_FINAL_AMOUNT_DEPOSIT = "SET_FINAL_AMOUNT_DEPOSIT";
    public static final String ADD_MOTO = "ADD_MOTO";
    public static final String FINISH_DEPOSIT = "FINISH_DEPOSIT";

    public static final String TYPE_QR_VNPAY = "QR_VNPAY";
    public static final String TYPE_QR_MOMO = "QR_MOMO";
    public static final String TYPE_QRCODE = "QRCODE";
    //    public static final String TYPE_PAY_QR_VAQR = "QR_VAQR";
    public static final String TYPE_CARD = "CARD";
    public static final String TYPE_DEPOSIT = "DEPOSIT";
    public static final String TYPE_SETFINAL_DEPOSIT = "TYPE_SETFINAL_DEPOSIT";
    public static final String TYPE_FINISH_DEPOSIT = "TYPE_FINISH_DEPOSIT";
    public static final String TYPE_MOTO = "MOTO";
    public static final String TYPE_MOTO_DEPOSIT = "MOTO_DEPOSIT";
    public static final String TYPE_VOID = "TYPE_VOID";
    public static final String TYPE_CANCEL = "TYPE_CANCEL";

    //todo NextPay QR
//    public static final String TYPE_NP_QR_VA = "NP_QR_VA";           //todo code emart_vietqr use: "QR_VAQR"
    public static final String VietQR = "VAQR";           //todo code emart_vietqr use: "NPQR_VA"
//    public static final String ZALOPAY = "ZALOPAY";           //todo code emart_vietqr use: "NPQR_VA"
    public static final String ZALOPAY = "ZALOPAY";           //todo code emart_vietqr use: "NPQR_VA"
    Socket socket;
    String ipServer;
    DataInputStream dataInputStream;
    DataOutputStream dataOutputStream;
    private static final int PORT = 1110;
    ExecutorService executor = Executors.newSingleThreadExecutor();
    MainActivity.ItfCallbackLog itfCallbackLog;
    DataDepositTcp dataDepositTcp;
    DataOrder dataOrder;
    DataMoto dataMoto;
    DataVoid dataVoid;
    DataStatusDevice dataStatusDevice;
    String mess;
    Context context;
//    SaveLogController logController;

    public SocketClient() {
    }

    //*  ACTION SEND DATA  *//
    private void sendMsg(String service) throws IOException {
        dataOutputStream = new DataOutputStream(socket.getOutputStream());
        switch (service) {
            case ADD_ORDER_INFOR:
            case CANCEL_ORDER:
                String data = MyGson.getGson().toJson(dataOrder) + "nextpay";
                sentData(data);
                break;
            case ADD_DEPOSIT:
            case SET_FINAL_AMOUNT_DEPOSIT:
            case FINISH_DEPOSIT:
                String data1 = MyGson.getGson().toJson(dataDepositTcp) + "nextpay";
                sentData(data1);
                break;
            case ADD_MOTO: {
                String data2 = MyGson.getGson().toJson(dataMoto) + "nextpay";
                sentData(data2);
                break;
            }
            case "GET_STATUS_DEVICE": {
                String data4 = MyGson.getGson().toJson(dataStatusDevice) + "nextpay";
                sentData(data4);
                break;
            }
            case serviceVoidTrans: {
                String data3 = MyGson.getGson().toJson(dataVoid) + "nextpay";
                sentData(data3);
                break;
            }
        }
    }

    private void sentData(String data) throws IOException {
        Log.d(TAG, "data send=: " + data);
        byte[] dataInBytes = data.getBytes(StandardCharsets.UTF_8);
        dataOutputStream.write(dataInBytes, 0, dataInBytes.length);
    }

//    public void setLogController(SaveLogController logController) {
//        this.logController = logController;
//    }

    public void setContext(Context context) {
        this.context = context;
    }

    public void pushPayment(String service) {
        if (executor.isTerminated()) {
            executor.shutdown();
        }
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    saveLAct("Open socket");
                    socket = new Socket(ipServer, PORT);
                    socket.setKeepAlive(true);
                    boolean isAlive = socket.getKeepAlive();
                    Log.d(TAG, "run: connect Success");

                    // todo send order
                    sendMsg(service);
                    saveLAct("send success");
                    Log.d(TAG, "run: send data oki");
                    boolean checkEndString = false;

                    //todo result data
                    saveLAct("socket isConnected: " + socket.isConnected() + " isAlive: " + isAlive);
                    while (socket.isConnected() && isAlive) {
                        //todo receive data
                        StringBuilder data = new StringBuilder();
                        String clearData = "";
                        dataInputStream = new DataInputStream(new BufferedInputStream(socket.getInputStream()));

                        while (true) {
                            try {
                                byte b = dataInputStream.readByte();
                                data.append((char) b);
                                if (data.toString().endsWith("}") && data.toString().contains("NOTIFICATION")) {
                                    break;
                                }
                            } catch (Exception e) {
                                Log.d(TAG, "readByte: err " + e);
                                socket.close();
                                break;
                            }
                        }
                        mess = data.toString();

                        Log.d(TAG, "doInBackground: mess= " + mess);
                        saveLAct("result= " + mess);
                        itfCallbackLog.onResult(mess);
                    }
                } catch (IOException e) {
                    //TODO handler client Socket Err (eg: show dialog Err, ...)
                    saveLAct("scoket err: " + e.getMessage());
                    Log.e(TAG, "onCreate: " + e.getMessage());
                    itfCallbackLog.onResult("connect err= " + e.getMessage());
                }

                pushlog();
            }
        });
    }


    private void closeSocket() {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void setIpServer(String ipServer) {
        this.ipServer = ipServer;
    }

    public void setItfCallbackLog(MainActivity.ItfCallbackLog itfCallbackLog) {
        this.itfCallbackLog = itfCallbackLog;
    }


    private void saveLAct(String text) {
//        logController.appendLogAction(text);
    }

    private void savelog() {
//        logController.saveLog();
    }

    private void pushlog() {
//        logController.pushLog(true);
    }

    public void setDataDepositTcp(DataDepositTcp dataDepositTcp) {
        this.dataDepositTcp = dataDepositTcp;
    }

    public void setDataOrder(DataOrder dataOrder) {
        this.dataOrder = dataOrder;
    }

    public void setDataVoid(DataVoid dataVoid) {
        this.dataVoid = dataVoid;
    }

    public void setDataStatusDevice(DataStatusDevice dataStatusDevice) {
        this.dataStatusDevice = dataStatusDevice;
    }

    public void setDataMoto(DataMoto dataMoto) {
        this.dataMoto = dataMoto;
    }
}
