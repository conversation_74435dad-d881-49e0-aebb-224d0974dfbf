package com.xthuan.clientsocket;

public class DataPushpayment {
    private String serviceName;
    private String orderId;
    private String posId;
    private String amount;
    private String description;
    private String depositId;

    public DataPushpayment(String serviceName, String orderId, String posId, String amount, String description) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.posId = posId;
        this.amount = amount;
        this.description = description;
    }

    public DataPushpayment(String serviceName, String orderId, String posId, String amount) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.posId = posId;
        this.amount = amount;
    }


    // Getter Methods

    public String getServiceName() {
        return serviceName;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getPosId() {
        return posId;
    }

    public String getAmount() {
        return amount;
    }

    public String getDescription() {
        return description;
    }

    // Setter Methods

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDepositId() {
        return depositId;
    }

    public void setDepositId(String depositId) {
        this.depositId = depositId;
    }
}