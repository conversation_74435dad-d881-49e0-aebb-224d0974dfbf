package com.xthuan.clientsocket.screen;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.os.StrictMode;
import android.util.Log;

import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import com.google.android.material.textfield.TextInputEditText;
import com.xthuan.clientsocket.DataDepositTcp;
import com.xthuan.clientsocket.DataMoto;
import com.xthuan.clientsocket.DataOrder;
import com.xthuan.clientsocket.DataStatusDevice;
import com.xthuan.clientsocket.DataVoid;
//import com.xthuan.clientsocket.Mpos.SaveLogController;
import com.xthuan.clientsocket.R;
import com.xthuan.clientsocket.SocketClient;

import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Enumeration;

import static com.xthuan.clientsocket.SocketClient.*;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {
    String TAG = "MainActivity";

    TextView txt_log;
    TextInputEditText edt_ip_server, edt_orderID, edtTransID,
            edt_amount_deposit, edt_amount, edt_depositID, edt_phone,
            edt_moto_id, edt_pan_moto, edt_exp_date_moto,
            edt_cvv_moto, edt_cardHolderName_moto, edt_ota_moto;

    Spinner spnAutoDismissDlg, spnConfirmVoid, spnPermitPrintReceipt;
    SocketClient socketClient;
//    SaveLogController logController;

    int autoDissmissDlg = -2, autoPrintReceiptVoid = -2, confirmVoidValue = -2;

    int countPayment = 0;
    String methodStatus = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        StrictMode.ThreadPolicy policy = new StrictMode.ThreadPolicy.Builder().permitAll().build();
        StrictMode.setThreadPolicy(policy);

//        logController = SaveLogController.getInstance(this);

        initView();
    }

    private void initView() {
        txt_log = findViewById(R.id.txt_log);
        edt_ip_server = findViewById(R.id.edt_ip_server);
        edt_orderID = findViewById(R.id.edt_orderID);
        edtTransID = findViewById(R.id.edt_transID);
        edt_amount = findViewById(R.id.edt_amount);
        edt_amount_deposit = findViewById(R.id.edt_amount_deposit);
        edt_depositID = findViewById(R.id.edt_depositID);
        edt_phone = findViewById(R.id.edt_phone);
        edt_moto_id = findViewById(R.id.edt_moto_id);
        edt_pan_moto = findViewById(R.id.edt_pan_moto);
        edt_exp_date_moto = findViewById(R.id.edt_exp_date_moto);
        edt_cvv_moto = findViewById(R.id.edt_cvv_moto);
        edt_cardHolderName_moto = findViewById(R.id.edt_cardHolderName_moto);
        edt_ota_moto = findViewById(R.id.edt_ota_moto);

        initSpiner();
    }

    private void initSpiner() {
        spnAutoDismissDlg = findViewById(R.id.spnAutoDismissDlg);
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                R.array.autoDismissDialog_value, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spnAutoDismissDlg.setAdapter(adapter);
        spnAutoDismissDlg.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0) {
                    autoDissmissDlg = -2;
                } else {
                    autoDissmissDlg = Integer.parseInt(String.valueOf(parent.getItemAtPosition(position)));
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        /* -------------- */
        spnConfirmVoid = findViewById(R.id.spnConfirmVoid);
        ArrayAdapter<CharSequence> adapter1 = ArrayAdapter.createFromResource(this,
                R.array.confirmVoid_value, android.R.layout.simple_spinner_item);
        adapter1.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spnConfirmVoid.setAdapter(adapter1);
        spnConfirmVoid.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0) {
                    confirmVoidValue = -2;
                } else {
                    confirmVoidValue = Integer.parseInt(String.valueOf(parent.getItemAtPosition(position)));
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        /* -------------- */
        spnPermitPrintReceipt = findViewById(R.id.spnPermitPrintReceipt);
        ArrayAdapter<CharSequence> adapter2 = ArrayAdapter.createFromResource(this,
                R.array.autoPrintVoid_value, android.R.layout.simple_spinner_item);
        adapter2.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spnPermitPrintReceipt.setAdapter(adapter2);
        spnPermitPrintReceipt.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0) {
                    autoPrintReceiptVoid = -2;
                } else {
                    autoPrintReceiptVoid = Integer.parseInt(String.valueOf(parent.getItemAtPosition(position)));
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void showText(String string) {
        runOnUiThread(() -> {
            txt_log.setText("\n" + txt_log.getText() + " \n" + string);
        });
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.payCard:
                buildData(SocketClient.TYPE_CARD);
                break;
            case R.id.btn_qrVNpay:
                buildData(SocketClient.TYPE_QR_VNPAY);
                break;
            case R.id.btn_qrMOMO:
                buildData(SocketClient.TYPE_QR_MOMO);
                break;
            case R.id.btn_qrZalo:
                buildData(SocketClient.ZALOPAY);
                break;
            case R.id.btn_qrVaQr:
                buildData(SocketClient.TYPE_QRCODE);
                break;
            case R.id.btn_void_trans:
                buildData(SocketClient.TYPE_VOID);
                break;
            case R.id.clear_log:
                clearLogMsg();
                break;
            case R.id.check_server:
                checkStatusSocket();
                break;
            case R.id.get_info_device:
                getInfoDevice();
                break;
            case R.id.get_status_battery:
                methodStatus = "GET_BATTERY_STATUS";
                getInfoDevice();
                break;
            case R.id.get_status_internet:
                methodStatus = "GET_INTERNET_STATUS";
                getInfoDevice();
                break;
            case R.id.cancel_trans:
                buildData(SocketClient.TYPE_CANCEL);
                break;
            case R.id.btn_deposit:
                buildData(SocketClient.TYPE_DEPOSIT);
                break;
            case R.id.btn_setFinalAmount_deposit:
                buildData(SocketClient.TYPE_SETFINAL_DEPOSIT);
                break;
            case R.id.btn_finish_deposit:
                buildData(SocketClient.TYPE_FINISH_DEPOSIT);
                break;
            case R.id.btn_moto:
                buildData(SocketClient.TYPE_MOTO);
                break;
            case R.id.btn_moto_deposit:
                buildData(SocketClient.TYPE_MOTO_DEPOSIT);
                break;
            default:
                break;
        }
    }

    private void getInfoDevice() {
        buildData("GET_STATUS_DEVICE");
    }

    private void checkStatusSocket() {
        String ipLan = loadIpLAN();
        Log.d(TAG, "onCreate: ipLan= " + ipLan);
        txt_log.setText("ipLan= " + ipLan);
        saveLAct("IpLan= " + ipLan);
    }

    private void clearLogMsg() {
        txt_log.setText("");
        try {
            InetAddress myHost = InetAddress.getLocalHost();
            System.out.println(myHost.getHostAddress());
            System.out.println(myHost.getHostName());
        } catch (UnknownHostException ex) {
            System.err.println("Cannot find local host");
        }
    }

    private void buildData(String typePayment) {
        countPayment++;
        edt_orderID.setText("orderID" + countPayment);
        socketClient = new SocketClient();
//        socketClient.setLogController(logController);
        socketClient.setItfCallbackLog(msg -> showText(msg));
        socketClient.setIpServer(edt_ip_server.getText().toString());
        socketClient.setContext(MainActivity.this);

        String serviceName = "";

        switch (typePayment) {
            case SocketClient.TYPE_CANCEL:
                serviceName = CANCEL_ORDER;
                socketClient.setDataOrder(new DataOrder(CANCEL_ORDER, edt_orderID.getText().toString(),
                        Long.parseLong(edt_amount.getText().toString().trim()), "", typePayment));
                break;
            case SocketClient.TYPE_VOID:
                serviceName = serviceVoidTrans;
                DataVoid dataVoid = new DataVoid(serviceVoidTrans, edt_orderID.getText().toString(), edtTransID.getText().toString().trim());
                if (autoPrintReceiptVoid != -2) {
                    dataVoid.setPermitPrintReceipt(autoPrintReceiptVoid);
                }
                if (confirmVoidValue != -2) {
                    dataVoid.setConfirmVoid(confirmVoidValue);
                }
                socketClient.setDataVoid(dataVoid);
                break;
            case SocketClient.TYPE_DEPOSIT:
                serviceName = ADD_DEPOSIT;
                DataDepositTcp dataDepositTcp = new DataDepositTcp(SocketClient.ADD_DEPOSIT, edt_orderID.getText().toString(),
                        Long.parseLong(edt_amount.getText().toString().trim()), edt_depositID.getText().toString().trim().toUpperCase(), edt_phone.getText().toString().trim());
                if (autoDissmissDlg != -2) {
                    dataDepositTcp.setAutoDismissDlgTimer(autoDissmissDlg);
                }
                socketClient.setDataDepositTcp(dataDepositTcp);
                break;
            case SocketClient.TYPE_SETFINAL_DEPOSIT:
                serviceName = SET_FINAL_AMOUNT_DEPOSIT;
                DataDepositTcp dataDepositTcp1 = new DataDepositTcp(SocketClient.SET_FINAL_AMOUNT_DEPOSIT, Long.parseLong(edt_amount.getText().toString().trim()), edt_depositID.getText().toString().trim().toUpperCase());
                socketClient.setDataDepositTcp(dataDepositTcp1);
                break;
            case SocketClient.TYPE_FINISH_DEPOSIT:
                serviceName = FINISH_DEPOSIT;
                DataDepositTcp dataDepositTcp2 = new DataDepositTcp(SocketClient.FINISH_DEPOSIT, Long.parseLong(edt_amount_deposit.getText().toString().trim()),
                        Long.parseLong(edt_amount.getText().toString().trim()), edt_depositID.getText().toString().trim().toUpperCase(), edtTransID.getText().toString().trim());
                socketClient.setDataDepositTcp(dataDepositTcp2);
                break;
            case SocketClient.TYPE_MOTO:
                serviceName = ADD_MOTO;
                DataMoto dataMoto = new DataMoto(ADD_MOTO, edt_orderID.getText().toString(), Long.parseLong(edt_amount.getText().toString().trim()), edt_pan_moto.getText().toString().trim(), edt_cardHolderName_moto.getText().toString().trim(), edt_exp_date_moto.getText().toString().trim(), edt_cvv_moto.getText().toString().trim(), edt_phone.getText().toString().trim(), "");
                socketClient.setDataMoto(dataMoto);
                break;
            case "GET_STATUS_DEVICE":
                serviceName = "GET_STATUS_DEVICE";
                DataStatusDevice dataStatusDevice = new DataStatusDevice(serviceName);
                if (!methodStatus.isEmpty()) {
                    dataStatusDevice.setMethod(methodStatus);
                }
                socketClient.setDataStatusDevice(dataStatusDevice);
                break;
            default:
                serviceName = ADD_ORDER_INFOR;
                DataOrder dataOrder = new DataOrder(SocketClient.ADD_ORDER_INFOR, edt_orderID.getText().toString(),
                        Long.parseLong(edt_amount.getText().toString().trim()), "", typePayment);
                if (typePayment.equals(SocketClient.TYPE_QRCODE)) {
                    //todo -> Ufo app đang chỉ hỗ trợ VAQR vì chưa có nút các phương thức khác :D, Bác nào maintain thì xử chỗ này nhé! ^ ^
                    dataOrder.setPaymentMethod(SocketClient.VietQR);
                }
                if (autoDissmissDlg != -2) {
                    dataOrder.setAutoDismissDlgTimer(autoDissmissDlg);
                }
                socketClient.setDataOrder(dataOrder);
                break;
        }

        socketClient.pushPayment(serviceName);
    }

    public interface ItfCallbackLog {
        void onResult(String msg);
    }

    private String loadIpLAN() {
        String ipAddress = null;
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface thisInterface = networkInterfaces.nextElement();
                Enumeration<InetAddress> inetAddresses = thisInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress thisAddress = inetAddresses.nextElement();
                    ipAddress = ipAddress + " ---- name= " + thisAddress.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return ipAddress;
    }

    private void saveLAct(String text) {
//        logController.appendLogAction(text);
    }

    public static String sha256(String base) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(base.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();

            for (int i = 0; i < hash.length; ++i) {
                String hex = Integer.toHexString(255 & hash[i]);
                if (hex.length() == 1) {
                    hexString.append('0');
                }

                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception var6) {
            throw new RuntimeException(var6);
        }
    }

    private String buildCheckSumCreateOrder(String udid, String phone, String amount, String email, String currency,
                                            String buyer_fullname, String buyer_address, String language, String returnUrl, String url_callback_err, String url_callback_notify) {
        return md5("7" + '|' + udid + '|' +
                "" + '|' + amount + '|' + currency + '|' +
                buyer_fullname + '|' + email + '|' +
                phone + '|' + buyer_address + '|' + returnUrl +
                '|' + url_callback_err + '|' + url_callback_notify + '|' + language + '|' +
                "123456789");
    }

    public static String md5(String string) {
        byte[] hash;
        try {
            hash = MessageDigest.getInstance("MD5").digest(string.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException var8) {
            throw new RuntimeException("Huh, MD5 should be supported?", var8);
        } catch (UnsupportedEncodingException var9) {
            throw new RuntimeException("Huh, UTF-8 should be supported?", var9);
        }

        StringBuilder hex = new StringBuilder(hash.length * 2);
        byte[] var3 = hash;
        int var4 = hash.length;

        for (int var5 = 0; var5 < var4; ++var5) {
            byte b = var3[var5];
            int i = b & 255;
            if (i < 16) {
                hex.append('0');
            }

            hex.append(Integer.toHexString(i));
        }

        return hex.toString();
    }
}