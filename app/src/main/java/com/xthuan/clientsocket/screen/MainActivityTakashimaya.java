package com.xthuan.clientsocket.screen;

import android.os.Bundle;

import android.view.View;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.textfield.TextInputEditText;
import com.xthuan.clientsocket.*;

import java.net.InetAddress;
import java.net.UnknownHostException;

import static com.xthuan.clientsocket.SocketClientTakashimaya.*;

public class MainActivityTakashimaya extends AppCompatActivity implements View.OnClickListener  {

    TextView txt_log;
    TextInputEditText edt_ip_server, edt_orderID, edtTransID,
    edt_amount, edt_phone;

    SocketClientTakashimaya socketClient;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_takashimaya);

        initView();
    }

    private void initView() {
        txt_log = findViewById(R.id.txt_log);
        edt_ip_server = findViewById(R.id.edt_ip_server);
        edt_orderID = findViewById(R.id.edt_orderID);
        edtTransID = findViewById(R.id.edt_transID);
        edt_amount = findViewById(R.id.edt_amount);
        edt_phone = findViewById(R.id.edt_phone);
    }

    private void showText(String string) {
        runOnUiThread(() -> {
            txt_log.setText("\n" + txt_log.getText() + " \n" + string);
        });
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.payCard:
                buildData(SocketClient.TYPE_CARD);
                break;
            case R.id.btn_qrVNpay:
                buildData(SocketClient.TYPE_QR_VNPAY);
                break;
            case R.id.btn_qrMOMO:
                buildData(SocketClient.TYPE_QR_MOMO);
                break;
            case R.id.btn_qrZalo:
                buildData(SocketClient.ZALOPAY);
                break;
            case R.id.btn_qrVaQr:
                buildData(SocketClient.TYPE_QRCODE);
                break;
            case R.id.btn_void_trans:
                buildData(SocketClient.TYPE_VOID);
                break;
            case R.id.clear_log:
                clearLogMsg();
                break;
            case R.id.check_server:
//                checkStatusSocket();
                break;
            case R.id.get_info_device:
                buildData("GET_STATUS_DEVICE");
                break;
            case R.id.cancel_trans:
                buildData(SocketClient.TYPE_CANCEL);
                break;
            default:
                break;
        }
    }

    private void clearLogMsg() {
        txt_log.setText("");
//        try {
//            InetAddress myHost = InetAddress.getLocalHost();
//            System.out.println(myHost.getHostAddress());
//            System.out.println(myHost.getHostName());
//        } catch (UnknownHostException ex) {
//            System.err.println("Cannot find local host");
//        }
    }

    int countPayment = 0;
    private void buildData(String typePayment) {
        countPayment++;
        edt_orderID.setText("orderID" + countPayment);
        socketClient = new SocketClientTakashimaya();
        socketClient.setItfCallbackLog(msg -> showText(msg));
        socketClient.setIpServer(edt_ip_server.getText().toString());
        socketClient.setContext(MainActivityTakashimaya.this);

        String serviceName = "";

        switch (typePayment) {
            case SocketClient.TYPE_CANCEL:
                serviceName = CANCEL_ORDER;
                socketClient.setDataOrder(new DataOrder(CANCEL_ORDER, edt_orderID.getText().toString(),
                        Long.parseLong(edt_amount.getText().toString().trim()), "", typePayment));
                break;
            case SocketClient.TYPE_VOID:
                serviceName = VOID_TRANSACTION;
                DataVoid dataVoid = new DataVoid(VOID_TRANSACTION, edt_orderID.getText().toString(), edtTransID.getText().toString().trim());
//                if (autoPrintReceiptVoid != -2) {
//                    dataVoid.setPermitPrintReceipt(autoPrintReceiptVoid);
//                }
//                if (confirmVoidValue != -2) {
//                    dataVoid.setConfirmVoid(confirmVoidValue);
//                }
                socketClient.setDataVoid(dataVoid);
                break;

//            case "GET_STATUS_DEVICE":
//                serviceName = "GET_STATUS_DEVICE";
//                DataStatusDevice dataStatusDevice = new DataStatusDevice(serviceName);
//                if (!methodStatus.isEmpty()) {
//                    dataStatusDevice.setMethod(methodStatus);
//                }
//                socketClient.setDataStatusDevice(dataStatusDevice);
//                break;
            default:
                serviceName = ADD_ORDER_INFOR;
                DataOrder dataOrder = new DataOrder(SocketClient.ADD_ORDER_INFOR, edt_orderID.getText().toString(),
                        Long.parseLong(edt_amount.getText().toString().trim()), "", typePayment);
                if (typePayment.equals(SocketClient.TYPE_QRCODE)) {
                    dataOrder.setPaymentMethod(SocketClientTakashimaya.VietQR);
                }

                socketClient.setDataOrder(dataOrder);
                break;
        }

        socketClient.pushPayment(serviceName);
    }
}