package com.xthuan.clientsocket.screen;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import com.xthuan.clientsocket.R;

public class HomeActivity extends AppCompatActivity {

    Button btnGoPushPayment;
    Button btnGoSocket;
    Button btnGoTakashimaya;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        btnGoPushPayment = findViewById(R.id.btn_go_pushPayment);
        btnGoSocket = findViewById(R.id.btn_go_socket);
        btnGoTakashimaya = findViewById(R.id.btn_go_Takashimaya);

        btnGoPushPayment.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(HomeActivity.this, ActivityPushPayment.class));
            }
        });

        btnGoSocket.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(HomeActivity.this, MainActivity.class));

            }
        });

        btnGoTakashimaya.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(HomeActivity.this, MainActivityTakashimaya.class));

            }
        });
//
//        Intent intent=new Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS");
//        startActivity(intent);
    }
}