package com.xthuan.clientsocket.screen;

import android.util.Log;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.spec.MGF1ParameterSpec;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;
import javax.crypto.spec.SecretKeySpec;

//import android.util.Base64;

public class EncodeDecode {

    private static final String TAG = "EncodeDecode";
    
    private static Key generateKey(String keyValue) {
//        byte[] keyBytes = new byte[]{};
        if (keyValue.length() > 16) {
            keyValue = keyValue.substring(0, 16); // truncate to length = 16
        } else {
            keyValue = String.format("%1$-16s", keyValue).replace(" ", "F"); // add padding "F" while length < 16
        }
        byte[] keyBytes = keyValue.getBytes(); // generate key byte from 16 length key value
        return new SecretKeySpec(keyBytes, "AES");
    }

    public static final int AES_KEY_SIZE = 256;
    public static final int GCM_IV_LENGTH = 12;
    public static final int GCM_TAG_LENGTH = 16;

    //message= {"amount":"1000","description":"this is description","orderId":"order1","posId":"mpos1","serviceName":"ADD_ORDER_INFOR"}

    public static String encryptAES(String secretKey, String message) throws Exception {
        // Chuyển đổi chuỗi secretKey thành byte array
        byte[] keyBytes = secretKey.getBytes(StandardCharsets.UTF_8);

        // Tạo khóa bí mật từ byte array
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");

        // Khởi tạo đối tượng Cipher với chế độ AES/ECB/PKCS5Padding
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);

        // Mã hóa thông điệp thành mảng byte
        byte[] encryptedBytes = cipher.doFinal(message.getBytes(StandardCharsets.UTF_8));

        // Chuyển đổi mảng byte thành chuỗi Base64
//        String encryptedMessage = Base64.getEncoder().encodeToString(encryptedBytes);
        String encryptedMessage = Base64.encodeBytes(encryptedBytes);

        return encryptedMessage;
    }
}
