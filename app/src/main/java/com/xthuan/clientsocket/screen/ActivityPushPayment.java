package com.xthuan.clientsocket.screen;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.textfield.TextInputEditText;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.xthuan.clientsocket.DataPushpayment;
import com.xthuan.clientsocket.Mpos.MposRestClient;
import com.xthuan.clientsocket.MyGson;
import com.xthuan.clientsocket.R;

import org.json.JSONObject;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.client.HttpClient;
import cz.msebera.android.httpclient.entity.StringEntity;
import cz.msebera.android.httpclient.impl.client.DefaultHttpClient;

public class ActivityPushPayment extends AppCompatActivity implements View.OnClickListener {

    private static String TAG = ActivityPushPayment.class.getSimpleName();

    TextInputEditText edtOrderID, edtPosId, edtAmount, edtDescription, edtMerchantID, edtSecretKey, edtDepositId;
    CheckBox cb_url_push_prod;
    TextView result;

    private static final String serviceName     = "ADD_ORDER_INFOR";
    private static final String serviceNameDeposit     = "ADD_DEPOSIT";
    private static final String serviceNameRemoteOrder     = "REMOVE_ORDER_INFOR";

//    private static final String URL_PUSHPAYMENT_DEV = "https://devapi.mpos.vn/supermarket/";
    private static final String URL_PUSHPAYMENT_DEV = "http://localhost:8089/order/";
    private static final String URL_PUSHPAYMENT = "https://api.mpos.vn/supermarket/";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_push_payment);

        initView();
    }

    private void initView() {
        edtOrderID = findViewById(R.id.edt_orderID);
        edtPosId = findViewById(R.id.edt_posId);
        edtAmount = findViewById(R.id.edt_amount);
        edtDescription = findViewById(R.id.edt_description);
        edtMerchantID = findViewById(R.id.edt_merchantID);
        edtSecretKey = findViewById(R.id.edt_secretKey);
        cb_url_push_prod = findViewById(R.id.cb_url_push_prod);
        edtDepositId = findViewById(R.id.edt_deposit);
        result = findViewById(R.id.result);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_pushPayment:
                onPushOrder();
                break; 
            case R.id.btn_deposit:
                onPushOrderDeposit();
                break;
                case R.id.btn_remote_order:
                onPressRemoteOrder();
                break;
            default:
                break;
        }
    }

    private void onPressRemoteOrder() {
        if (checkValidateData()) {
            DataPushpayment dataPushpayment = new DataPushpayment(serviceNameRemoteOrder, edtOrderID.getText().toString(),
                    edtPosId.getText().toString().trim(),
                    edtAmount.getText().toString().trim());

            dataPushpayment.setDepositId(edtDepositId.getText().toString().trim());

            String data = MyGson.getGson().toJson(dataPushpayment);
            Log.d(TAG, "onPushOrder: " + data);
            result.setText(data);
            String eData = null;
            try {
                eData = EncodeDecode.encryptAES(edtSecretKey.getText().toString().trim(), data);
                Log.d(TAG, "onPushOrder: eData= " + eData);
            } catch (Exception e) {
                e.printStackTrace();
            }

            sendOrder(eData);
        }
    }

    private void onPushOrderDeposit() {
        if (checkValidateDataDeoposit()) {
            DataPushpayment dataPushpayment = new DataPushpayment(serviceNameDeposit, edtOrderID.getText().toString(),
                    edtPosId.getText().toString().trim(),
                    edtAmount.getText().toString().trim(),
                    edtDescription.getText().toString().trim());

            dataPushpayment.setDepositId(edtDepositId.getText().toString().trim());

            String data = MyGson.getGson().toJson(dataPushpayment);
            Log.d(TAG, "onPushOrder: " + data);
            result.setText(data);
            String eData = null;
            try {
                eData = EncodeDecode.encryptAES(edtSecretKey.getText().toString().trim(), data);
                Log.d(TAG, "onPushOrder: eData= " + eData);
            } catch (Exception e) {
                e.printStackTrace();
            }

            sendOrder(eData);
        }
    }

    private boolean checkValidateDataDeoposit() {
        if (edtAmount.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập amout");
            return false;
        }

        if (edtMerchantID.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập merchantID: Mở portal -> tích hợp kết nối");
            return false;
        }

        if (edtOrderID.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập orderID");
            return false;
        }

        if (edtPosId.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập posID");
            return false;
        }

        if (edtSecretKey.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập key");
            return false;
        }

        if (edtDepositId.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập depositId");
            return false;
        }

        return true;
    }

    private void onPushOrder() {
        if (checkValidateData()) {
            DataPushpayment dataPushpayment = new DataPushpayment(serviceName, edtOrderID.getText().toString(),
                    edtPosId.getText().toString().trim(),
                    edtAmount.getText().toString().trim(),
                    edtDescription.getText().toString().trim());

            String data = MyGson.getGson().toJson(dataPushpayment);
            Log.d(TAG, "onPushOrder: " + data);
            result.setText(data);
            String eData = null;
            try {
                eData = EncodeDecode.encryptAES(edtSecretKey.getText().toString().trim(), data);
                Log.d(TAG, "onPushOrder: eData= " + eData);
            } catch (Exception e) {
                e.printStackTrace();
            }

            sendOrder(eData);

        }
    }

    private void sendOrder(String eData) {
        String url;
        if (cb_url_push_prod.isChecked()) {
            url = URL_PUSHPAYMENT;
        } else {
            url = URL_PUSHPAYMENT_DEV;
        }
        result.append("\nurl: " + url);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("merchantId", edtMerchantID.getText().toString().trim());
            jo.put("reqData", eData);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Log.e(TAG, "sendOrder: ", e1);
        }

        MposRestClient.getInstance(ActivityPushPayment.this).post(ActivityPushPayment.this, url, entity,
                "application/json; charset=utf-8", new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        Log.d(TAG, "onSuccess: push");
                        showToast("onSuccess errCode= ");
                        result.append("\nSuccess");
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Log.d(TAG, "onFailure: push");
                        showToast("onFailure errCode= ");
                        result.append("\nonFailure");
                    }
                });
    }

    private boolean checkValidateData() {
        if (edtAmount.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập amout");
            return false;
        }

        if (edtMerchantID.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập merchantID: Mở portal -> tích hợp kết nối");
            return false;
        }

        if (edtOrderID.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập orderID");
            return false;
        }

        if (edtPosId.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập posID");
            return false;
        }

        if (edtSecretKey.getText().toString().trim().isEmpty()) {
            showToast("Vui lòng nhập key");
            return false;
        }

        return true;
    }

    private void showToast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }
}