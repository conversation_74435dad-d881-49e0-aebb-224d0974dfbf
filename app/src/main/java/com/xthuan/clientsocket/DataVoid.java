package com.xthuan.clientsocket;

public class DataVoid {
    String serviceName;
    String orderId;
    String transCode;
    String paymentIdentifier;
    int confirmVoid;
    int permitPrintReceipt;

    public DataVoid() {

    }

    public DataVoid(String serviceName, String orderId, String transCode) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.transCode = transCode;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getPaymentIdentifier() {
        return paymentIdentifier;
    }

    public void setPaymentIdentifier(String paymentIdentifier) {
        this.paymentIdentifier = paymentIdentifier;
    }

    public int getConfirmVoid() {
        return confirmVoid;
    }

    public void setConfirmVoid(int confirmVoid) {
        this.confirmVoid = confirmVoid;
    }

    public int getPermitPrintReceipt() {
        return permitPrintReceipt;
    }

    public void setPermitPrintReceipt(int permitPrintReceipt) {
        this.permitPrintReceipt = permitPrintReceipt;
    }
}
