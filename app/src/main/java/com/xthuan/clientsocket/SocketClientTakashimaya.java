package com.xthuan.clientsocket;

import android.content.Context;
import android.util.Log;
import com.xthuan.clientsocket.screen.MainActivity;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SocketClientTakashimaya {
    private static final String TAG = SocketClientTakashimaya.class.getSimpleName();
    /** Service Name **/
    /** Service Name **/
    public static final String ADD_ORDER_INFOR = "ADD_ORDER_INFOR";
    public static final String CANCEL_ORDER = "CANCEL_ORDER";
    public static final String VOID_TRANSACTION = "VOID_TRANSACTION";

    String key = "X7WVTM4TOEALACIUOOZACRZA";

    // Transaction type: 1 for SALE, 2 for REFUND, 3 for VOID, 4 for TRANSFER, 5 for SETTLE

    private static final int SALE       = 1;
    private static final int REFUND     = 2;
    private static final int VOID       = 3;
    private static final int TRANSFER   = 4;
    private static final int SETTLE     = 5;

    //todo NextPay QR
//    public static final String TYPE_NP_QR_VA = "NP_QR_VA";           //todo code emart_vietqr use: "QR_VAQR"
    public static final String VietQR = "VAQR";           //todo code emart_vietqr use: "NPQR_VA"
//    public static final String ZALOPAY = "ZALOPAY";           //todo code emart_vietqr use: "NPQR_VA"
    public static final String ZALOPAY = "ZALOPAY";           //todo code emart_vietqr use: "NPQR_VA"
    Socket socket;
    String ipServer;
    DataInputStream dataInputStream;
    DataOutputStream dataOutputStream;
    private static final int PORT = 1110;
    ExecutorService executor = Executors.newSingleThreadExecutor();
    MainActivity.ItfCallbackLog itfCallbackLog;
    DataOrder dataOrder;
    DataMoto dataMoto;
    DataVoid dataVoid;
    DataStatusDevice dataStatusDevice;
    String mess;
    Context context;
//    SaveLogController logController;

    public SocketClientTakashimaya() {
    }

    //*  ACTION SEND DATA  *//
    private void sendMsg(String service) throws Exception {
        dataOutputStream = new DataOutputStream(socket.getOutputStream());
        switch (service) {
            case ADD_ORDER_INFOR:
                sentData(_buildDataAddOrder());
                break;
            case CANCEL_ORDER:
                break;
            case "GET_STATUS_DEVICE": {
                String data4 = MyGson.getGson().toJson(dataStatusDevice) + "nextpay";
                sentData(data4);
                break;
            }
            case VOID_TRANSACTION: {
                sentData(_buildDataVoid()); //20250513135921073
                break;
            }
        }
    }

    private String _buildDataVoid() {
        Map<String, String> data = new HashMap<>();
        data.put("APP", "MPOS");
        data.put("TXN_TYPE", String.valueOf(VOID));
//        data.put("AMOUNT", buildAmount(String.valueOf(dataVoid.get())));
        data.put("INVOICE", dataVoid.getTransCode());
//        data.put("CURRENCY_CODE", 704);
        data.put("PRINT_MSG", "REF1010085");
        data.put("SEND", "OK");

        return buildDataString(data);
    }

    private String _buildDataAddOrder() {
        Map<String, String> data = new HashMap<>();
        data.put("APP", "MPOS");
        if (dataOrder.getPaymentMethod() != null &&  dataOrder.getPaymentMethod().equals(VietQR)) {
            data.put("TXN_TYPE", String.valueOf(TRANSFER));
        }else {
            data.put("TXN_TYPE", "1");
        }
        data.put("AMOUNT", buildAmount(String.valueOf(dataOrder.getTotalAmount())));
        data.put("BILL_ID", dataOrder.getOrderId());
//        data.put("CURRENCY_CODE", 704);
        data.put("PRINT_MSG", "REF1010085");
        data.put("SEND", "OK");

        return buildDataString(data);
    }

    public String buildDataString(Map<String, String> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) return "";

        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            builder.append(entry.getKey())
                    .append(":")
                    .append(entry.getValue())
                    .append(";");
        }
        return builder.toString();
    }

    public String buildAmount(String amountStr) {
        if (!amountStr.matches("\\d+")) {
            throw new IllegalArgumentException("Invalid amount: must be digits only");
        }
        return String.format("%012d", Long.parseLong(amountStr));
    }

    // Parse ngược lại: bỏ padding 0 bên trái
    public String parseAmount(String amount12Digits) {
        if (amount12Digits.length() != 12 || !amount12Digits.matches("\\d+")) {
            throw new IllegalArgumentException("Invalid 12-digit amount string");
        }
        return String.valueOf(Long.parseLong(amount12Digits)); // Bỏ 0 đầu
    }

    private void sentData(String data) throws Exception {
        Log.d(TAG, "data send=: " + data);
//        byte[] dataInBytes = data.getBytes(StandardCharsets.UTF_8);
//        dataOutputStream.write(dataInBytes, 0, dataInBytes.length);

        //todo test
//        data = "APP:MPOS;BILL_ID:orderID1;AMOUNT:000000000200;TXN_TYPE:1;SEND:OK;PRINT_MSG:REF1010085;";

//        byte[] dataOutBytes = buildEncryptedMessage(data, key);
        byte[] dataOutBytes = PacketCrypto.buildEncryptedMessage(data, key);
        Log.d(TAG, "data send length : " + dataOutBytes.length);
        Log.d(TAG, "data send=: " + dataOutBytes.length);


        //test decrypt data
//        InputStream inputStream = new DataInputStream(new ByteArrayInputStream(dataOutBytes));
//        DataInputStream streamData = new DataInputStream(inputStream);
//        String deData = PacketCrypto.decryptPacket(streamData, key);
//        Log.d(TAG, "deData: " + deData);

        dataOutputStream.write(dataOutBytes, 0, dataOutBytes.length);
    }

    public byte[] buildEncryptedMessage(String msg, String keyStr) throws Exception {
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv = new byte[8]; // 8 bytes zero IV
        byte[] plainData = msg.getBytes(StandardCharsets.US_ASCII);

        // Encrypt DATA
        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // Padding manually to make multiple of 8
        int padLen = 8 - (plainData.length % 8);
        byte[] paddedPlainData = Arrays.copyOf(plainData, plainData.length + padLen);

        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encryptedData = cipher.doFinal(paddedPlainData);

        // Message length
        int len = encryptedData.length;

        // Construct packet
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(0x02);                       // STX
        baos.write((len >> 8) & 0xFF);         // MSGLEN High byte
        baos.write(len & 0xFF);                // MSGLEN Low byte
        baos.write(encryptedData);             // Encrypted DATA
        baos.write(0x03);                      // ETX

        byte[] withoutLRC = baos.toByteArray();
        byte lrc = 0x00;
        for (int i = 1; i < withoutLRC.length; i++) {
            lrc ^= withoutLRC[i];              // LRC = XOR from MSGLEN to ETX
        }

        baos.write(lrc);                       // LRC
        return baos.toByteArray();
    }


    public String decryptReceivedMessage(byte[] received, String keyStr) throws Exception {
        if (received[0] != 0x02) throw new IllegalArgumentException("Invalid STX");

        int len = ((received[1] & 0xFF) << 8) | (received[2] & 0xFF);
        byte[] encryptedData = Arrays.copyOfRange(received, 3, 3 + len);

        if (received[3 + len] != 0x03) throw new IllegalArgumentException("Invalid ETX");

        // LRC check
        byte calcLRC = 0x00;
        for (int i = 1; i <= 3 + len; i++) {
            calcLRC ^= received[i];
        }

        if (calcLRC != received[4 + len]) throw new IllegalArgumentException("Invalid LRC");

        // Decrypt
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv = new byte[8];

        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(encryptedData);

        return new String(decrypted, StandardCharsets.US_ASCII).trim(); // Remove padding zeros
    }


    public Map<String, String> parseDecryptedData(String data) {
        Map<String, String> result = new LinkedHashMap<>();
        if (data == null || data.isEmpty()) return result;

        // Loại bỏ dấu ; cuối nếu có
        if (data.endsWith(";")) {
            data = data.substring(0, data.length() - 1);
        }

        String[] pairs = data.split(";");
        for (String pair : pairs) {
            int index = pair.indexOf(":");
            if (index != -1) {
                String key = pair.substring(0, index).trim();
                String value = pair.substring(index + 1).trim();
                result.put(key, value);
            }
        }
        return result;
    }


    public void setContext(Context context) {
        this.context = context;
    }

    public void pushPayment(String service) {
        if (executor.isTerminated()) {
            executor.shutdown();
        }
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    saveLAct("Open socket");
                    socket = new Socket(ipServer, PORT);
                    socket.setKeepAlive(true);
                    boolean isAlive = socket.getKeepAlive();
                    Log.d(TAG, "run: connect Success");

                    // todo send order
                    try {
                        sendMsg(service);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    saveLAct("send success");
                    Log.d(TAG, "run: send data oki");

                    //todo result data
                    saveLAct("socket isConnected: " + socket.isConnected() + " isAlive: " + isAlive);
                    while (socket.isConnected() && isAlive) {
                        //todo receive data
                        StringBuilder data = new StringBuilder();
                        String clearData = "";
                        dataInputStream = new DataInputStream(new BufferedInputStream(socket.getInputStream()));

                        try {
                            clearData = PacketCrypto.decryptPacket(dataInputStream, "X7WVTM4TOEALACIUOOZACRZA");
                            Log.d(TAG, "run: clear data oki: " + clearData);
                            mess = clearData;
                            Log.d(TAG, "doInBackground: mess= " + mess);
                            saveLAct("result= " + mess);
                            itfCallbackLog.onResult(mess);
                        } catch (Exception e) {
                            isAlive = false;
                            socket.close();
                            Log.e(TAG, "readByte: err " + e);
                        }

//                        while (true) {
//                            try {
//                                byte b = dataInputStream.readByte();
//                                data.append((char) b);
//                                if (data.toString().endsWith("}") && data.toString().contains("NOTIFICATION")) {
//                                    break;
//                                }
//                            } catch (Exception e) {
//                                Log.d(TAG, "readByte: err " + e);
//                                socket.close();
//                                break;
//                            }
//                        }
                    }
                } catch (IOException e) {
                    //TODO handler client Socket Err (eg: show dialog Err, ...)
                    saveLAct("scoket err: " + e.getMessage());
                    Log.e(TAG, "onCreate: " + e.getMessage());
                    itfCallbackLog.onResult("connect err= " + e.getMessage());
                }

                pushlog();
            }
        });
    }


    private void closeSocket() {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void setIpServer(String ipServer) {
        this.ipServer = ipServer;
    }

    public void setItfCallbackLog(MainActivity.ItfCallbackLog itfCallbackLog) {
        this.itfCallbackLog = itfCallbackLog;
    }


    private void saveLAct(String text) {
//        logController.appendLogAction(text);
    }

    private void savelog() {
//        logController.saveLog();
    }

    private void pushlog() {
//        logController.pushLog(true);
    }

    public void setDataOrder(DataOrder dataOrder) {
        this.dataOrder = dataOrder;
    }

    public void setDataVoid(DataVoid dataVoid) {
        this.dataVoid = dataVoid;
    }

    public void setDataStatusDevice(DataStatusDevice dataStatusDevice) {
        this.dataStatusDevice = dataStatusDevice;
    }

    public void setDataMoto(DataMoto dataMoto) {
        this.dataMoto = dataMoto;
    }
}
