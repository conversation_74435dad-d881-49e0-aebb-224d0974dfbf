//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xthuan.clientsocket;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.Primitives;

public class MyGson {
    private static Gson gson;
    private static MyGson myGson;

    public MyGson() {
    }

    public static synchronized MyGson getInstance() {
        if (myGson == null) {
            myGson = new MyGson();
        }

        return myGson;
    }

    public static synchronized Gson getGson() {
        if (gson == null) {
            GsonBuilder gsonBuilder = new GsonBuilder();
            gson = gsonBuilder.create();
        }

        return gson;
    }

    public static <T> T parseJson(String data, Class<T> anonymousClass) {
        Object object = null;

        try {
            object = getGson().fromJson(data, anonymousClass);
        } catch (Exception var12) {
            var12.printStackTrace();
        } finally {
            try {
                if (object == null) {
                    object = anonymousClass.newInstance();
                }
            } catch (Exception var11) {
                var11.printStackTrace();
            }

        }

        return Primitives.wrap(anonymousClass).cast(object);
    }
}
