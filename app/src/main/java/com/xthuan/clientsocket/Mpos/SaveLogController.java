////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//package com.xthuan.clientsocket.Mpos;
//
//import android.content.Context;
//import android.content.SharedPreferences;
//import android.content.SharedPreferences.Editor;
//import android.content.pm.PackageInfo;
//import android.content.pm.PackageManager;
//import android.content.pm.PackageManager.NameNotFoundException;
//import android.os.Looper;
//import android.os.Build.VERSION;
//import android.text.TextUtils;
//import android.util.Log;
//
//import com.loopj.android.http.AsyncHttpResponseHandler;
//import cz.msebera.android.httpclient.Header;
//import cz.msebera.android.httpclient.entity.StringEntity;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.text.SimpleDateFormat;
//import java.util.Calendar;
//
//public class SaveLogController {
//    private static final String TAG = "SaveLogController";
//    public static final String LOG_CRASH = " LOGCRASH \n";
//    public static final String LOG_VOID_FAIL_TRANS = "____LOG_VOID_FAIL_TRANS____\n";
//    public static final String TEXT_REQUEST = "REQ";
//    public static final String TEXT_ACTION = "ACT";
//    public static final String TEXT_RESPONSE = "RES";
//    public static final String TEXT_EXCEPTION = "EXC";
//    public static final String TEXT_CALLBACK = "ACB";
//    public static final String FORMAT_MSG = "%s | %s | %s %n";
//    public static final String SUCCESS = " success ";
//    public static final String FAILURE = " failure ";
//    public static final String TIMEOUT = " timeout ";
//    private final String logDataKey = "LOG_DATA";
//    private final SharedPreferences mSharedPreferences;
//    String os = "";
//    String model = "";
//    String dateTemp = "";
//    String extraText = "";
//    Context context;
//    StringBuffer logData;
//    SaveLogController.OnResultPushLog callback;
//    static SaveLogController saveLogController;
//
//
//    public static synchronized SaveLogController getInstance(Context c) {
////        Utils.LOGD(TAG, "getInstance:");
//        if (saveLogController == null || saveLogController.isContextNull()) {
//            saveLogController = new SaveLogController(c);
////            Utils.LOGD(TAG, "new object");
//        }
//        return saveLogController;
//    }
//
//    public SaveLogController(Context c) {
//        this.context = c;
//        mSharedPreferences = context.getApplicationContext().getSharedPreferences("ufothuan_test_socket", Context.MODE_PRIVATE);
//        initLogData();
//    }
//
//    private void initLogData() {
//        this.dateTemp = "";
//        this.logData = new StringBuffer();
//    }
//
//    public void setExtraText(String extraText) {
//        this.extraText = extraText;
//    }
//
//    public boolean isContextNull() {
//        return this.context == null;
//    }
//
//    public void setCallback(SaveLogController.OnResultPushLog callback) {
//        this.callback = callback;
//    }
//
//    public void saveLog() {
//        if (this.logData != null) {
//            this.saveLog(this.logData);
//            this.initLogData();
//        }
//
//    }
//
//    public void saveLog(StringBuffer log) {
//        if (log != null) {
//            this.saveLog(log.toString());
//        }
//
//    }
//
//    public void saveLog(String log) {
//        if (!TextUtils.isEmpty(log)) {
//            this.saveLogToStorage(this.mSharedPreferences.getString("LOG_DATA", "") + "\n" + log);
//        }
//
//    }
//
//    private void saveLogToStorage(String content) {
//        Editor editor = this.mSharedPreferences.edit();
//        editor.putString("LOG_DATA", content);
//        editor.apply();
//    }
//
//    private String getLogSaved() {
//        return this.mSharedPreferences.getString("LOG_DATA", "");
//    }
//
//    public void appendLogAction(String log) {
//        this.appendLog("ACT", log);
//    }
//
//    public void appendLogActionCallback(String log) {
//        this.appendLog("ACB", log);
//    }
//
//    public void appendLogResponse(String log) {
//        this.appendLog("RES", log);
//    }
//
//    public void appendLogResponseSuccess(String log) {
//        this.appendLog("RES", " success " + log);
//    }
//
//    public void appendLogResponseFail(String log) {
//        this.appendLog("RES", " failure " + log);
//    }
//
//    public void appendLogException(String log) {
//        this.appendLog("EXC", log);
//    }
//
//    public void appendLogExceptionTimeout(String log) {
//        this.appendLog("EXC", " timeout " + log);
//    }
//
//    public void appendLogException(String log, String exception) {
//        this.appendLog("EXC", log + " -- " + exception);
//    }
//
//    public void appendLogFail(String log, byte[] error) {
//        this.appendLog("REQ", log + ":>>" + (error != null ? new String(error) : ""));
//    }
//
//    public void appendLogRequestApi(String log) {
//        this.appendLog("REQ", log);
//    }
//
//    public void appendLogRequestApiFail(String log, byte[] error) {
//        this.appendLogFail(log, error);
//    }
//
//    public void appendLogRequestApiFail(String log, byte[] error, Throwable arg3) {
//        this.appendLogFail(String.format("%s:%s", log, arg3 == null ? "" : arg3.getMessage()), error);
//    }
//
//    public void appendLog(String log) {
//        this.appendLog("ACT", log);
//    }
//
//    public void appendLog(String action, String log) {
//        if (this.logData != null) {
//            if (TextUtils.isEmpty(this.dateTemp)) {
//                this.dateTemp = convertTimestamp(System.currentTimeMillis(), 2);
//                this.logData.append(String.format("**%s%n", this.dateTemp));
//            }
//
//            this.logData.append(String.format("%s | %s | %s %n", convertTimestamp(System.currentTimeMillis(), 5), action, log));
//        }
//
//    }
//
//    public void pushLog() {
//        this.pushLog(false);
//    }
//
//    public void pushLog(boolean runInThread) {
//        Log.d("SaveLogController", "pushLog: ==>");
//        String oldLog = this.getLogSaved();
//        if (this.context != null && (this.logData != null && this.logData.length() != 0 || !TextUtils.isEmpty(oldLog))) {
//            if (runInThread) {
//                Looper.prepare();
//            }
//
//            this.saveLog();
//            final String logSend = this.getLogSaved();
//            this.saveLogToStorage("");
//            PackageInfo info = null;
//
//            try {
//                PackageManager manager = this.context.getPackageManager();
//                info = manager.getPackageInfo(this.context.getPackageName(), 0);
//            } catch (NameNotFoundException var12) {
//                Log.e("SaveLogController", "pushLog: ", var12);
//            }
//
//            StringEntity entity = null;
//
//            try {
//                this.os = "android " + VERSION.RELEASE;
//                this.model = "testsocket";
//                String name = "ufothuan";
//                String userId = "ufothuan";
//                String log = " userId: " + userId + " name: " + name + " EMAIL_MERCHANT: " + "| model: " + this.model + "| App version: " + (info == null ? "(null)" : info.versionCode) + "| os: " + this.os + " (" + VERSION.SDK_INT + ")| SDK-vc=" + 2 + "| pn=" + this.context.getPackageName() + (TextUtils.isEmpty(this.extraText) ? "" : "| extraText: " + this.extraText) + "\n" + logSend;
//                JSONObject jRoot = new JSONObject();
//                jRoot.put("serviceName", "SAVE_LOG_ACTION");
//                jRoot.put("os", "ANDROID");
//                jRoot.put("description", log);
//                jRoot.put("muid", userId);
//                entity = new StringEntity(jRoot.toString(), "UTF-8");
//            } catch (JSONException var10) {
//                Log.e("SaveLogController", "pushLog: ", var10);
//            } catch (OutOfMemoryError var11) {
//                this.saveLogToStorage("--OutOfMemoryError--");
//            }
//
//            MposRestClient.getInstance(this.context).post(this.context, "https://devapi.mpos.vn/api", entity, "application/json; charset=utf-8", new AsyncHttpResponseHandler() {
//                public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
//                    Log.d("SaveLogController", "saveLog success:" + new String(arg2));
//                    if (SaveLogController.this.callback != null) {
//                        SaveLogController.this.callback.onSuccessPushLog();
//                    }
//
//                }
//
//                public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//                    Log.d("SaveLogController", "saveLog error:" + arg3.getMessage());
//                    SaveLogController.this.saveLogToStorage(logSend + "\n" + SaveLogController.this.getLogSaved());
//                    SaveLogController.this.callOnFailureLog();
//                }
//            });
//            if (runInThread) {
//                Looper.loop();
//            }
//
//        } else {
//            Log.d("SaveLogController", "--no logdata or dataStore is null----");
//            this.callOnFailureLog();
//        }
//    }
//
//    private void callOnFailureLog() {
//        if (this.callback != null) {
//            this.callback.onFailurePushLog();
//        }
//
//    }
//
//    public interface OnResultPushLog {
//        void onSuccessPushLog();
//
//        void onFailurePushLog();
//    }
//
//    public static String convertTimestamp(long timeStamp, int flag) {
//        switch (flag) {
//            case 1 :
//                return convertTimestamp(timeStamp, "HH:mm");
//            case 2 :
//                return convertTimestamp(timeStamp, "dd/MM/yyyy");
//            case 3 :
//                return convertTimestamp(timeStamp, "dd/MM/yyyy HH:mm:ss");
//            case 4 :
//                return convertTimestamp(timeStamp, "HH:mm:ss dd/MM/yyyy");
//            case 5 :
//                return convertTimestamp(timeStamp, "HH:mm:ss");
//        }
//        return "";
//    }
//    public static String convertTimestamp(long timeStamp, String format) {
//        Calendar mydate = Calendar.getInstance();
//        mydate.setTimeInMillis(timeStamp);
//
//        try {
//            return new SimpleDateFormat(format).format(mydate.getTime());
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "";
//        }
//    }
//
//
//}
