package com.xthuan.clientsocket.Mpos;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;
import android.text.TextUtils;
import android.widget.Toast;

import java.math.BigInteger;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.ByteOrder;
import java.util.Enumeration;

import static android.content.Context.WIFI_SERVICE;

//import android.net.http.AndroidHttpClient;

//import org.apache.http.HttpResponse;
//import org.apache.http.NameValuePair;
//import org.apache.http.client.HttpClient;
//import org.apache.http.client.entity.UrlEncodedFormEntity;
//import org.apache.http.client.methods.HttpGet;
//import org.apache.http.client.methods.HttpPost;
//import org.apache.http.impl.client.DefaultHttpClient;
//import org.apache.http.params.HttpConnectionParams;

//import java.net.URI;
//import java.util.ArrayList;

public class GetData {

//	public static String convertStreamToString(InputStream is) {
//		if (is != null) {
//			Writer writer = new StringWriter();
//
//			char[] buffer = new char[1024];
//			try {
//				Reader reader = null;
//				try {
//					reader = new BufferedReader(new InputStreamReader(is, "UTF-8"), 1024);
//				} catch (UnsupportedEncodingException e) {
//					e.printStackTrace();
//				}
//                if (reader == null) {
//                    return "";
//                }
//                int n;
//				try {
//					while ((n = reader.read(buffer)) != -1) {
//						writer.write(buffer, 0, n);
//					}
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			} finally {
//				try {
//					is.close();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//			return writer.toString();
//		} else {
//			return "";
//		}
//	}

//	public static String getDataOverNet(String remoteUrl) {
//		// System.out.println("Link: " + remoteUrl);
//		String result = "";
//		InputStream data = null;
//        AndroidHttpClient httpClient = null;
//		try {
//            httpClient = AndroidHttpClient.newInstance("Android");
//			HttpConnectionParams.setSoTimeout(httpClient.getParams(), 45000);
//			HttpConnectionParams.setConnectionTimeout(httpClient.getParams(), 45000);
//			URI uri = new URI(remoteUrl);
//			HttpGet method = new HttpGet(uri);
//			HttpResponse response = httpClient.execute(method);
//			data = response.getEntity().getContent();
//		} catch (Exception e) {
//			e.printStackTrace();
//			return result;
//		}finally {
//            if (httpClient != null) {
//                httpClient.close();
//            }
//        }
//
//		result = convertStreamToString(data);
//		return result;
//	}

	public static boolean CheckInternet(Context c) {
		ConnectivityManager connec = (ConnectivityManager) c.getSystemService(Context.CONNECTIVITY_SERVICE);
        android.net.NetworkInfo wifi = null;
        android.net.NetworkInfo mobile = null;
        android.net.NetworkInfo wan = null;
        if (connec != null) {
            wifi = connec.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            mobile = connec.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
            wan = connec.getNetworkInfo(ConnectivityManager.TYPE_ETHERNET);
        }
        if (wifi!=null && wifi.isConnected()) {
			return true;
//		} else if (!mobile.isConnected()) {
//			return false;
		}
		else if (mobile!=null && mobile.isConnected()) {
			return true;
		}
		else if (wan!=null && wan.isConnected()) {
            return true;
        }
		return false;
	}
	
	public static boolean CheckInternet(final Context c, final String msgError){
		boolean result = CheckInternet(c);
		if(!result && !TextUtils.isEmpty(msgError)){
			try {
				((Activity) c).runOnUiThread(new Runnable() {
					public void run() {
						Toast.makeText(c, msgError, Toast.LENGTH_LONG).show();
					}
				});
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return result;
	}

//	public static String getList(String url) {
//		return getDataOverNet(url);
//	}

//	public static String sendPostData(String url, ArrayList<NameValuePair> postData) throws IOException {
//		HttpClient httpclient = new DefaultHttpClient();
//		HttpPost httppost = new HttpPost(url);
//		HttpResponse hp;
//		httppost.setEntity(new UrlEncodedFormEntity(postData));
//		hp = httpclient.execute(httppost);

//		String data;
//		try {
//			InputStream ins = hp.getEntity().getContent();
//			data = GetData.convertStreamToString(ins);
//		} catch (IllegalStateException e) {
//			e.printStackTrace();
//			return "";
//		} catch (IOException e) {
//			e.printStackTrace();
//			return "";
//		}
//		return data;
//	}
	
//	public static String sendPostData(String url) throws IOException {
//		HttpClient httpclient = new DefaultHttpClient();
//		HttpPost httppost = new HttpPost(url);
//		HttpResponse hp;
//		hp = httpclient.execute(httppost);
//
//		String data;
//		try {
//			InputStream ins = hp.getEntity().getContent();
//			data = GetData.convertStreamToString(ins);
//		} catch (IllegalStateException e) {
//			e.printStackTrace();
//			return "";
//		} catch (IOException e) {
//			e.printStackTrace();
//			return "";
//		}
//		System.out.println("Result: " + data);
//		return data;
//	}

	public static String MD5(String md5) {
		try {
			java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
			byte[] array = md.digest(md5.getBytes());
			StringBuilder sb = new StringBuilder();
            for (byte anArray : array) {
                sb.append(Integer.toHexString((anArray & 0xFF) | 0x100).substring(1, 3));
            }
			return sb.toString();
		} catch (java.security.NoSuchAlgorithmException e) {
            e.printStackTrace();
		}
		return null;
	}

	public static String loadIpSocketServer(Context context) {
		WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
		int ipAddress = wifiManager.getConnectionInfo().getIpAddress();

		// Convert little-endian to big-endianif needed
		if (ByteOrder.nativeOrder().equals(ByteOrder.LITTLE_ENDIAN)) {
			ipAddress = Integer.reverseBytes(ipAddress);
		}

		byte[] ipByteArray = BigInteger.valueOf(ipAddress).toByteArray();

		String ipAddressString;
		try {
			ipAddressString = InetAddress.getByAddress(ipByteArray).getHostAddress();
		} catch (UnknownHostException ex) {
			ipAddressString = null;
		}

		return ipAddressString;
	}

	public static String loadIpSocketServer() {
		String ipAddress = null;
		try {
			for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
				NetworkInterface intf = en.nextElement();
				for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
					InetAddress inetAddress = enumIpAddr.nextElement();
					if (!inetAddress.isLoopbackAddress()) {
						ipAddress = inetAddress.getHostAddress().toString();
					}
				}
			}
		} catch (SocketException ex) {

		}

		return ipAddress;
	}

}
