package com.xthuan.clientsocket.Mpos;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.annotation.NonNull;

public final class PrefLibTV {

    private static final String TAG = "PrefLibTV";

    public static final String PREFIX_NUM_OF_WIDGET = "LIBNOTIFY";

    public static final String PREFIX_BLUETOOTH_NAME 	= "pref_bluetooth_name";
    public static final String PREFIX_BLUETOOTH_ADDRESS = "pref_bluetooth_address";
    public static final String PREFIX_IS_SAVE_LOGIN		= "pref_is_save_login";
    public static final String PREFIX_PW                = "pref_pou";
    public static final String PREFIX_EMAIL_MERCHANT	= "pref_email_merchant";
    public static final String PREFIX_RESTRICT_INTERNATIONAL_CARD	    = "pref_restrict_international_card";
    public static final String PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD	= "pref_emv_restrict_international_card";

    public static final String PREFIX_TID				= "pref_tid";
    public static final String PREFIX_MID				= "pref_mid";
    public static final String PREFIX_MERCHANT_ID		= "pref_merchantId";
    public static final String PREFIX_COUNT_FALL_BACK	= "pref_count_fall_back";

    //    public static final String SKIP_SIGNATURE           = "skip_signature";
    public static final String MAX_AMOUNT_SIGNATURE     = "max_amount_signature";

    public static final String PREFIX_FLAG_DEVICES		= "flag_devices";
    public static final String PREFIX_FLAG_SERVER		= "flag_server";
    // config issuerValuesList
    public static final String PREFIX_CONFIG_MASTER		= "pref_config_master";
    public static final String PREFIX_CONFIG_VISA		= "pref_config_visa";
    public static final String PREFIX_CONFIG_JCB		= "pref_config_jcb";

    public static final String PREFIX_WKK_MAILINH		= "wkk_mailinh";
    public static final String WORKING_KEY  			= "working_key";
    public static final String WORKING_KEY_LAST_OK      = "working_key_last_success";

    public static final String CUSTOM_LANGUAGE       	= "custom_language";
    public static final String MP_K                     = "mP_k";
    public static final String PACKAGE_NAME       	    = "package_name";
    public static final String BANK_NAME       	        = "bank_name";
    public static final String MPOS_TID       	        = "mpos-tid";
    public static final String MPOS_MID       	        = "mpos-mid";
    public static final String MPOS_MUID       	        = "mpos-muid";
    public static final String TKL2       	            = "pref_tkl2";

    public static final String PRE_SALE_CONFIG          = "pre_sale_config";

    public static final String BLOCK_PAN_RANGE       	= "block_pan_range";
    public static final String MERCHANT_INFO           	= "merchant_info"; // show in dialog error

    // version bin local
    public static final String currVerBinLocal      = "currVerBinLocal";
    public static final String serverVerBinLocal    = "serverVerBinLocal";
    public static final String listBinLocal         = "listBinLocal";
    public static final String reportError          = "reportError";

    public static final String lastTimeCallApi      = "lastTimeCA";     // check session for smart-pos
    private static final String lastErrorSale        = "lastErrorSale";     //
    public static final String lastErrorSaleTime    = "lastErrorSaleTime"; // last time save lastErrorSale

    public static final String installmentInfo      = "installmentInfo";

//    public static final String cacheMpos            = "cacheMpos";
//    public static final String lastTimeCacheMpos    = "lastTimeCacheMpos";

    public static final String LAST_TIME_LOGIN_MA = "last_time_login";
    public static final String LAST_TIME_LOAD_CONFIG_MC_OK = "last_time_load_config_mc_ok";
    public static final String DATA_LOGIN_CACHE         = "data_login_cache";

    public static final String versionLite          = "versionLite";
    public static final String versionLiteNew       = "versionLite_new";
    public static final String timeTrans            = "timeTrans";

    public static final String upgradeFw    = "upgradeFw";
    public static final String urlUpgradeFw = "urlUpgradeFw";
    public static final String versionFwUpgrade = "versionFwUpgrade";

    public static final String upgradeEMVConfig = "upgradeEMVConfig";
    public static final String urlEmvApp = "urlEmvApp";
    public static final String urlEmvCapk = "urlEmvCapk";

    public static final String dataEmvConfig = "dataEmvConfig";

    public static final String numSaleRemain = "numSaleRemain";

    public static final String readersInjected = "readersInjected";
    public static final String commonConfig = "commonConfig";
    public static final String lastTimeCommonConfigOk = "lastTimeCommonConfigOk";
    public static final String lastTimeCommonConfigFail = "lastTimeCommonConfigFail";


    public static final String canSaleService = "canSaleService";
    public static final String sendTrxReceipt = "sendTrxReceipt";   // app sent email or not => 1: app sent; other: mpos sent

    private String dataLoginMerchant = "dataLoginMerchant";

    private static final String BLUETOOTH_ADDRESS_PRINTER 	= "BLUETOOTH_ADDRESS_PRINTER";
    private static final String BLUETOOTH_NAME_PRINTER 	= "BLUETOOTH_NAME_PRINTER";
    private static final String BLUETOOTH_ADDRESS_PAX 	= "BLUETOOTH_ADDRESS_PAX";
    private static final String SSID_PAX 	= "SSID_PAX";

    private static String PREFIX_IS_DISABLE_CHECK_GPS = "IS_DISABLE_CHECK_GPS";

    private static final String ld = "lgdt";
    private static PrefLibTV prefLibTV;
    SharedPreferences sharedPreferences;

    private static final String isPrint = "isPrint";
    private static String binLocals = "binLocals";
    private static String isMerEmart = "isMerEmart";

    public static String hasPermitCachePreSale = "hasPermitCachePreSale";

    public static final String currencyCertify = "currencyCertify";

    private PrefLibTV(@NonNull Context context) {
//        Log.i(TAG, "PrefLibTV: constructor");
        if (context != null) {
            Log.i(TAG, "PrefLibTV: init sharePreference ok");
            sharedPreferences = context.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
            put(PACKAGE_NAME, context.getPackageName());
        }
    }

    public static synchronized PrefLibTV getInstance(@NonNull Context context) {
        if (context == null) {
            Log.e(TAG, "PrefLibTV: context must not null.");
        }
        if (prefLibTV == null || prefLibTV.isInitFail()) {
            prefLibTV = new PrefLibTV(context);
        }
        return prefLibTV;
    }

    public static String getBinLocals(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString(binLocals, "");
    }

    public static void setBinLocals(Context c, String bins) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(binLocals, bins);
        editor.apply();
    }

    public static boolean getMerEmart(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getBoolean(isMerEmart, false);
    }

    public static void saveMerEmart(Context c, boolean b) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(isMerEmart, b);
        editor.apply();
    }

    public static boolean getPermitPrint(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getBoolean(isPrint, true);
    }

    public static void setPermitPrint(Context c, boolean b) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(isPrint, b);
        editor.apply();
    }



    private boolean isInitFail() {
//        Log.i(TAG, "isInitOk: " + (sharedPreferences == null ? "not ok" : "ok"));
        return sharedPreferences == null;
    }

    public static void setPermitSettlement(Context c, boolean flag) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean("pref_permitSettlement", flag);
        editor.apply();
    }

    public static boolean getPermitSettlement(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean("pref_permitSettlement", false);
    }

    public static void setPermitVoid(Context c, boolean index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean("pref_permitVoid", index);
        editor.apply();
    }

    public static boolean getPermitVoid(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean("pref_permitVoid", false);
    }

    public static void setDisableCheckGps(Context c, boolean value) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(PREFIX_IS_DISABLE_CHECK_GPS, value);
        editor.apply();
    }

    public static boolean getDisableCheckGps(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean(PREFIX_IS_DISABLE_CHECK_GPS, false);
    }

    /**
     * set sessionkey and lasttime call api
     * @param c: context
     * @param index: session key
     */
    public static void setSessionKey(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("pref_session_key", index);
        editor.putLong(lastTimeCallApi, System.currentTimeMillis());
        editor.apply();
    }

    public static String getSessionKey(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("pref_session_key", "");
    }

    public static long getLastTimeCA(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getLong(lastTimeCallApi, System.currentTimeMillis());
    }

    /**
     * lastErrorSale: save last status transaction and current time
     */
    public static void setLastErrorSale(Context c, int status) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putInt(lastErrorSale, status);
        editor.putLong(lastErrorSaleTime, System.currentTimeMillis());
        editor.apply();
    }
    public static long getLastErrorSaleTime(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getLong(lastErrorSaleTime, 0);
    }

    public static void setTKL2(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(TKL2, index);
        editor.apply();
    }

    public static String getTKL2(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(TKL2, "");
    }

    public static void setSerialNumber(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("pref_serial_number", index);
        editor.apply();
    }

    public static String getSerialNumber(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("pref_serial_number", "");
    }

    // bluetooth name
    public static void setBluetoothName(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_BLUETOOTH_NAME, index);
        editor.apply();
    }

    public static String getBluetoothName(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_BLUETOOTH_NAME, "");
    }

    // bluetooth address
    public static void setBluetoothAddress(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_BLUETOOTH_ADDRESS, index);
        editor.apply();
    }

    public static String getBluetoothAddress(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_BLUETOOTH_ADDRESS, "");
    }

    // is save login
    public static void setIsSaveLogin(Context c, boolean index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(PREFIX_IS_SAVE_LOGIN, index);
        editor.apply();
    }

    public static boolean getIsSaveLogin(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean(PREFIX_IS_SAVE_LOGIN, false);
    }

    //
    public static void setRestrictInternationalCard(Context c, boolean index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(PREFIX_RESTRICT_INTERNATIONAL_CARD, index);
        editor.apply();
    }

    public static boolean getRestrictInternationalCard(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean(PREFIX_RESTRICT_INTERNATIONAL_CARD, false);
    }
    public static void setEmvRestrictInternationalCard(Context c, boolean index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD, index);
        editor.apply();
    }

    public static boolean getEmvRestrictInternationalCard(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean(PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD, false);
    }

    // email merchant
    public static void setEmailMerchant(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_EMAIL_MERCHANT, index);
        editor.apply();
    }

    public static String getEmailMerchant(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_EMAIL_MERCHANT, "");
    }
    // mid
    public static void setMId(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_MID, index);
        editor.apply();
    }

    public static String getMId(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_MID, "");
    }
    // tid
    public static void setTId(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_TID, index);
        editor.apply();
    }

    public static String getTId(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_TID, "");
    }
    // PREFIX_MERCHANT_ID
    public static void setMerchantId(Context c, String index) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_MERCHANT_ID, index);
        editor.apply();
    }

    public static String getMerchantsId(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(PREFIX_MERCHANT_ID, "");
    }

    public static void setUserId(Context c, String id) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("pref_user_id", id);
        editor.apply();
    }

    public static String getUserId(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("pref_user_id", "");
    }

    public static void setLatitude(Context c, String id) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("pref_latitude", id);
        editor.apply();
    }

    public static String getLatitude(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString("pref_latitude", "0");
    }

    public static void setLongtitude(Context c, String id) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("pref_longtitude", id);
        editor.apply();
    }

    public static String getLongtitude(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString("pref_longtitude", "0");
    }

    public static void setGa(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("lib_ga", ads);
        editor.apply();
    }

    public static String getGa(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("lib_ga", "UA-27981359-1");
    }

    public static void setShopName(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("shop_name", ads);
        editor.apply();
    }

    public static String getShopName(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("shop_name", "MPOS");
    }

    public static void setTagConfig(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("tag_config", ads);
        editor.apply();
    }

    public static String getTagConfig(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("tag_config", "");
    }

    public static void setCAKey(Context c, boolean ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean("ca_key", ads);
        editor.apply();
    }

    public static boolean getCAKey(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getBoolean("ca_key", false);
    }

    public static void setCAData(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("ca_data", ads);
        editor.apply();
    }

    public static String getCAData(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString("ca_data", "");
    }

    public static void setReaderEncryptMode(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("readerEncryptMode", ads);
        editor.apply();
    }

    public static String getReaderEncryptMode(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString("readerEncryptMode", "1");
    }

    public static void setFlagDevices(Context c, int ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putInt(PREFIX_FLAG_DEVICES, ads);
        editor.apply();
    }

    public static int getFlagDevices(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getInt(PREFIX_FLAG_DEVICES, 0);
    }

    public static void setFlagServer(Context c, int ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putInt(PREFIX_FLAG_SERVER, ads);
        editor.apply();
    }

    public static int getFlagServer(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getInt(PREFIX_FLAG_SERVER, 0);
    }

    public static void setPartner(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("flag_amount", ads);
        editor.apply();
    }

    public static String getPartner(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString("flag_amount","");
    }

    public static void setRouter(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString("flag_router", ads);
        editor.apply();
    }

    public static String getRouter(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString("flag_router","");
    }
    public static void setCountFallBack(Context c, int ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putInt(PREFIX_COUNT_FALL_BACK, ads);
        editor.apply();
    }

    public static int getCountFallBackk(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getInt(PREFIX_COUNT_FALL_BACK, 0);
    }
    // master
    public static void setTagConfigMaster(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_CONFIG_MASTER, ads);
        editor.apply();
    }

    public static String getTagConfigMaster(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString(PREFIX_CONFIG_MASTER, "");
    }
    // visa
    public static void setTagConfigVisa(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_CONFIG_VISA, ads);
        editor.apply();
    }

    public static String getTagConfigVisa(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString(PREFIX_CONFIG_VISA, "");
    }
    // jcb
    public static void setTagConfigJCB(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_CONFIG_JCB, ads);
        editor.apply();
    }

    public static String getTagConfigJCB(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        return settings.getString(PREFIX_CONFIG_JCB, "");
    }


    // data Login Merchant
    public void createDataLoginMerchant(String data) {
        put(dataLoginMerchant, data);
//        SharedPrefs.getInstance().put(dataLoginMerchant, data);
    }

    public String getDataLoginMerchant() {
//        return SharedPrefs.getInstance().get(dataLoginMerchant, String.class);
        return get(dataLoginMerchant, String.class, "");
    }


    // need signature
    /*public static void setSkipSignature(Context c, String value) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0 | Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(SKIP_SIGNATURE, value);
        editor.apply();
    }

    public static String getSkipSignature(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0 | Context.MODE_MULTI_PROCESS);
        return settings.getString(SKIP_SIGNATURE, "");
    }*/
    // max amount signature
    public static void setMaxAmountSignature(Context c, String value) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(MAX_AMOUNT_SIGNATURE, value);
        editor.apply();
    }

    // custom language
    public void saveCustomLanguage(String value) {
        put(CUSTOM_LANGUAGE, value);
    }

    public String getCustomLanguage() {
        return get(CUSTOM_LANGUAGE, String.class, "");
    }

    // BANK_NAME
    public void saveBankName(String value) {
        put(BANK_NAME, value);
    }

    public String getBankName() {
        return get(BANK_NAME, String.class, "");
    }

    // working key
    public static void setWorkingKey(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0 | Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(WORKING_KEY, ads);
        editor.apply();
    }

    public static String getWorkingKey(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0 | Context.MODE_MULTI_PROCESS);
        return settings.getString(WORKING_KEY, "");
    }


    public static void setWkkMaiLinh(Context c, String value) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(PREFIX_WKK_MAILINH, value);
        editor.apply();
    }

    //BLUETOOTH_ADDRESS_PRINTER
    public void createBluetoothAddressPrinter(String address, String name) {
        put(BLUETOOTH_ADDRESS_PRINTER, address);
        put(BLUETOOTH_NAME_PRINTER, name);
    }

    public String getBluetoothAddressPrinter() {
        return get(BLUETOOTH_ADDRESS_PRINTER, String.class);
    }
    public String getBluetoothNamePrinter() {
        return get(BLUETOOTH_NAME_PRINTER, String.class);
    }

    public void createBluetoothAddressPAX(String value) {
        put(BLUETOOTH_ADDRESS_PAX, value);
    }

    public String getBluetoothAddressPAX() {
        return get(BLUETOOTH_ADDRESS_PAX, String.class);
    }

    public void createSSIDPAX(String value) {
        put(SSID_PAX, value);
    }

    public String getSSIDPAX() {
        return get(SSID_PAX, String.class);
    }

    // Data Log
    //CHANGE CURRENTCY CERTIFY
    public static void setCurrencyCertify(Context c, String ads) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(currencyCertify, ads);
        editor.apply();
    }

    public static String getCurrencyCertify(Context c) {
        SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
        return settings.getString(currencyCertify, "");
    }


    // Data Log
    public void saveLogData(String data) {
        put(ld, data);
    }

    public String getLogData() {
        return get(ld, String.class);
    }

    public <T> void put(String key, T data) {
        if (sharedPreferences == null) {
            Log.e(TAG, "context is null");
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        if (data instanceof String) {
            editor.putString(key, (String) data);
        } else if (data instanceof Boolean) {
            editor.putBoolean(key, (Boolean) data);
        } else if (data instanceof Float) {
            editor.putFloat(key, (Float) data);
        } else if (data instanceof Integer) {
            editor.putInt(key, (Integer) data);
        } else if (data instanceof Long) {
            editor.putLong(key, (Long) data);
        }
//        else {
//            editor.putString(key, MyApplication.self().getGSon().toJson(data));
//        }
        editor.apply();
    }

    public <T> T get(String key, Class<T> anonymousClass) {
        if (anonymousClass == String.class) {
            return get(key, anonymousClass, "");
        } else if (anonymousClass == Boolean.class) {
            return get(key, anonymousClass, false);
        } else if (anonymousClass == Float.class) {
            return get(key, anonymousClass,  0);
        } else if (anonymousClass == Integer.class) {
            return get(key, anonymousClass,  0);
        } else if (anonymousClass == Long.class) {
            return get(key, anonymousClass,  0L);
        }
//        else {
//            return (T) MyApplication.self()
//                    .getGSon()
//                    .fromJson(mSharedPreferences.getString(key, ""), anonymousClass);
//        }
        return (T) "";
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> anonymousClass, Object defaultValue) {
        if (sharedPreferences == null) {
            Log.e(TAG, "context is null");
            return (T) defaultValue;
        }
        if (anonymousClass == String.class) {
            return (T) sharedPreferences.getString(key, String.valueOf(defaultValue));
        } else if (anonymousClass == Boolean.class) {
            return (T) Boolean.valueOf(sharedPreferences.getBoolean(key, (boolean) defaultValue));
        } else if (anonymousClass == Float.class) {
            return (T) Float.valueOf(sharedPreferences.getFloat(key, (Float) defaultValue));
        } else if (anonymousClass == Integer.class) {
            return (T) Integer.valueOf(sharedPreferences.getInt(key, (int) defaultValue));
        } else if (anonymousClass == Long.class) {
            return (T) Long.valueOf(sharedPreferences.getLong(key, (Long) defaultValue));
        } else {
            return (T) "";
        }
    }
}