package com.xthuan.clientsocket.Mpos;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.loopj.android.http.AsyncHttpClient;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.FileAsyncHttpResponseHandler;
import com.loopj.android.http.PersistentCookieStore;
import com.loopj.android.http.RequestParams;

import cz.msebera.android.httpclient.entity.StringEntity;

public class MposRestClient {
    static String TAG = "MposRestClient";
    private static MposRestClient mposRestClient;
	private static AsyncHttpClient client;
//	private static AsyncHttpClient client = new AsyncHttpClient(true, 80, 443);
	private static PersistentCookieStore cookie;
	private static final int MAX_RETRY_REQUEST = 0;
	private static final int TIME_OUT = 40000;
	private static final int TIME_OUT_PAYMENT = 60000;
	private static final int TIME_OUT_PAYMENT_MA = 70000;
//	private static final int TIME_OUT_PAYMENT = 120000;
//    private static final ExecutorService executorService = Executors.newFixedThreadPool(4);
	private static String userAgent = "";

	public static synchronized MposRestClient getInstance(Context context) {
//        Utils.LOGD(TAG, "getInstance: have context");
        initMyClient();

        boolean setCookie = false;
        if(cookie==null){
            cookie = new PersistentCookieStore(context);
            setCookie = true;
        }
        if (client != null && setCookie) {
            setCookieStore(cookie);
        }

		return mposRestClient;
	}

    public static synchronized MposRestClient getInstance() {
	    initMyClient();
        return mposRestClient;
    }

    private static synchronized void initMyClient() {
//        Utils.LOGD(TAG, "initMyClient: ");
        if (mposRestClient == null) {
            mposRestClient = new MposRestClient();
        }
        if (client == null) {
            client = new AsyncHttpClient();
//            client = new AsyncHttpClient(true, 80, 443);
        }
        client.setLoggingEnabled(true);
//        System.out.println("log enabled:"+client.isLoggingEnabled()+" level:"+client.getLoggingLevel());
        client.setMaxRetriesAndTimeout(MAX_RETRY_REQUEST, TIME_OUT);
        client.setTimeout(TIME_OUT);
//        client.setUserAgent("android-async-http-1.4.9");
//        client.addHeader("X-MACQ-UID", "");
//        client.setThreadPool(executorService);
//        client.setEnableRedirects(true);

        if (TextUtils.isEmpty(userAgent)) {
            userAgent = buildUserAgent();
        }
        client.setUserAgent(userAgent);
//        try {
//            customSslSocket();
//        } catch (KeyStoreException e) {
//            e.printStackTrace();
//        } catch (CertificateException e) {
//            e.printStackTrace();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        } catch (UnrecoverableKeyException e) {
//            e.printStackTrace();
//        } catch (KeyManagementException e) {
//            e.printStackTrace();
//        }
    }

    private static String buildUserAgent() {
        String deviceLevelUserAgent = null;
        try {
            deviceLevelUserAgent = System.getProperty( "http.agent" );
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(deviceLevelUserAgent)) {
//            String webUserAgent = WebSettings.getDefaultUserAgent(context);
//            Utils.LOGD(TAG, "buildUserAgent: 2--->" + webUserAgent);
            return "Default Android " + "/Android " + Build.VERSION.RELEASE + "/" + Build.MODEL;
        }
        return deviceLevelUserAgent;
    }


//    private static void customSslSocket() throws KeyStoreException, CertificateException, NoSuchAlgorithmException, IOException, UnrecoverableKeyException, KeyManagementException {
//        /// We initialize a default Keystore
//        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
//// We load the KeyStore
//        trustStore.load(null, null);
//// We initialize a new SSLSocketFacrory
//        TLSSocketFactory socketFactory = new TLSSocketFactory(trustStore);
//// We set that all host names are allowed in the socket factory
//        socketFactory.setHostnameVerifier(MySSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
//// We initialize the Async Client
////        AsyncHttpClient client = new AsyncHttpClient();
////// We set the timeout to 30 seconds
////        client.setTimeout(30*1000);
//// We set the SSL Factory
//        client.setSSLSocketFactory(socketFactory);
//// We initialize a GET http request
//
//    }
	
	public static AsyncHttpClient getClient() {
		return client;
	}

    public MposRestClient setPaymentTimeout(){
        client.setTimeout(TIME_OUT_PAYMENT);
        return mposRestClient;
    }
    public MposRestClient setPaymentMATimeout(){
        client.setTimeout(TIME_OUT_PAYMENT_MA);
        return mposRestClient;
    }

    public MposRestClient addHeader(String header, String value) {
	    client.addHeader(header, value);
        return mposRestClient;
    }

    public MposRestClient setCustomTimeout(int milisecon){
        if(milisecon<=20000){
            System.out.println("*** timeout need more than 20s ***");
        }
        else{
            client.setTimeout(milisecon);
        }
        return mposRestClient;
    }

	public static void get(String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.get(url, params, responseHandler);
	}
	public void get(String url, AsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.get(url, responseHandler);
	}

	public void get(String url, FileAsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.get(url, responseHandler);
	}

	public void post(Context c, String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
        if (GetData.CheckInternet(c, "Không có internet")) {
		    client.post(url, params, responseHandler);
        }
	}

    public void post(String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.post(url, params, responseHandler);
	}
    public void post(String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.post(null, url, entity, cType, responseHandler);
	}

	public static void post(String url, AsyncHttpResponseHandler responseHandler) {
		client.setTimeout(TIME_OUT);
		client.post(url, responseHandler);
	}

    public void post(Context c, String url, StringEntity entity, AsyncHttpResponseHandler responseHandler) {
		post(c, url, entity, "application/json; charset=utf-8", responseHandler, false);
    }
	public void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
		post(c, url, entity, cType, responseHandler, false);
	}
	public void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler, boolean checkInternet) {
    	if (checkInternet && !GetData.CheckInternet(c, "Không có internet")) {
			return;
    	}
		client.post(c, url, entity, cType, responseHandler);
    }

    public void put(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
        client.put(c, url, entity, cType, responseHandler);
    }
	/*public void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
    	if (GetData.CheckInternet(c,c.getString(R.string.check_internet))) {
    		client.post(c, url, entity, cType, responseHandler);
    	}
    }*/
    
    /*public void post(Context c, String url, ByteArrayEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
//    	public static void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
//        client.setTimeout(TIME_OUT);
    	client.post(c, url, entity, cType, responseHandler);
    }*/

	public static void setCookieStore(PersistentCookieStore cookieStore) {
		client.setCookieStore(cookieStore);
	}

	public static MposRestClient setUserAgent(){
		client.setUserAgent("android-async-http-1.4.9");
		return mposRestClient;
	}

	public static void cancelRQ() {
		client.cancelAllRequests(true);
	}
}
