package com.xthuan.clientsocket;
public class DataPrePayEMart {
    String serviceName;
    String status;
    String responseCode;
    String transDate;
    String transAmount;
    String issuerCode;
    String muid;
    String orderId;
    String authCode;
    String transCode;
    String paymentIdentifier;
    String cardHolderName;
    String pan;
    String responseMess;

    public DataPrePayEMart() {
    }

    public DataPrePayEMart(String serviceName, String status, String responseCode, String transDate, String transAmount, String issuerCode, String muid, String orderId, String authCode, String transCode, String paymentIdentifier, String cardHolderName, String pan) {
        this.serviceName = serviceName;
        this.status = status;
        this.responseCode = responseCode;
        this.transDate = transDate;
        this.transAmount = transAmount;
        this.issuerCode = issuerCode;
        this.muid = muid;
        this.orderId = orderId;
        this.authCode = authCode;
        this.transCode = transCode;
        this.paymentIdentifier = paymentIdentifier;
        this.cardHolderName = cardHolderName;
        this.pan = pan;
    }

    public DataPrePayEMart(String serviceName, String orderId, String responseMess) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.responseMess = responseMess;
    }

    public DataPrePayEMart(String amount, String paymentIdentifier) {
        this.transAmount = amount;
        this.paymentIdentifier = paymentIdentifier;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(String transAmount) {
        this.transAmount = transAmount;
    }

    public String getIssuerCode() {
        return issuerCode;
    }

    public void setIssuerCode(String issuerCode) {
        this.issuerCode = issuerCode;
    }

    public String getMuid() {
        return muid;
    }

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getPaymentIdentifier() {
        return paymentIdentifier;
    }

    public void setPaymentIdentifier(String paymentIdentifier) {
        this.paymentIdentifier = paymentIdentifier;
    }

    public String getCardHolderName() {
        return cardHolderName;
    }

    public void setCardHolderName(String cardHolderName) {
        this.cardHolderName = cardHolderName;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getResponseMess() {
        return responseMess;
    }

    public void setResponseMess(String responseMess) {
        this.responseMess = responseMess;
    }
}
