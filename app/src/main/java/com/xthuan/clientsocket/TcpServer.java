/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2017-11-6
 * Module Author: laiyi
 * Description:
 *
 * ============================================================================
 */
package com.xthuan.clientsocket;

import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

class TcpServer {
    public static final String TAG = TcpServer.class.getSimpleName();
    private static ServerSocket serverSocket;
    private static InputStream in;
    private static OutputStream out;

    public static final String serviceOrder = "ADD_ORDER_INFOR";
    public static final String serviceUpdateTrans = "SERVICE_UPDATE_TRANSACTION";
    public static final String serviceVoidTrans = "VOID_TRANSACTION";

    String orderId;

    TcpServer(String orderId) {
        this.orderId = orderId;
    }

    public void open() {
        try {

            serverSocket = new ServerSocket(1110, 100);

            Log.i(TAG, "socket success");
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }
    }

    public int waitClient() {
        if (serverSocket == null) {
            return -1;
        }
        while (true) {
            try {
                Socket clientSocket = serverSocket.accept();
                clientSocket.setSoTimeout(5 * 1000);
                String remoteIP = clientSocket.getInetAddress().getHostAddress();
                int remotePort = clientSocket.getLocalPort();
                out = clientSocket.getOutputStream();
                in = clientSocket.getInputStream();
                Log.i(TAG, "A client connected. IP:" + remoteIP + ", Port: " + remotePort);
                Log.i(TAG, "server: receiving.............");

                String data = rcvData();
                Log.d(TAG, "waitClient: " + data);
                if (data.equals("getOrder")) {
                    //todo test my func
                    String a = convertStrtoHex(MyGson.getGson().toJson(buildDataOrder()));
                    sendData(a);
                }

            } catch (IOException e) {
                Log.e(TAG, "", e);
            }
        }
    }

    public int sendData(String data) {
        if (out == null || data == null || data.isEmpty()) {
            return -1;
        }

        Log.i(TAG, "write data :" + data);
        //todo test my func
        byte[] bytesss = hexStringToByteArray(data);
        try {
            out.flush();
//            out.write((data + "\n").getBytes());
            //todo test my func
            out.write(bytesss);
            out.flush();
            Log.d(TAG, "sendData: OK");
            return 0;
        } catch (Exception e) {
            Log.e(TAG, "", e);
        }

        return -1;
    }


    public void close() {
        try {
            if (serverSocket != null) {
                Log.i(TAG, "close serverSocket");
                serverSocket.close();
            }
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }
    }

    public String rcvData() {
        if (in == null) {
            return null;
        }

        byte[] data = new byte[2048];
        int index = 0;
        while (true) {
            try {
                int tmp = in.read();
                if (tmp == -1) {
                    return null;
                }

                if (tmp == 0x0D) {
                    continue;
                }

                if (tmp == 0x0A) {
                    break;
                }
//
//                if (index >= 2048) {
//                    return null;
//                }

                data[index] = (byte) tmp;
                index++;
            } catch (Exception e) {
                Log.e(TAG, "", e);
                return null;
            }
        }
        byte[] readData = new byte[index];
        System.arraycopy(data, 0, readData, 0, index);
        Log.i(TAG, "read data :" + new String(readData));
        return new String(readData);
    }

    private String convertStrtoHex(String str) {
//         return Integer.toHexString(Integer.parseInt(str));
        StringBuffer sb = new StringBuffer();
        char ch[] = str.toCharArray();
        for(int i = 0; i < ch.length; i++) {
            String hexString = Integer.toHexString(ch[i]);
            sb.append(hexString);
        }

//         char[] chars = Hex.encodeHex(str.getBytes(StandardCharsets.UTF_8));

        Log.d(TAG, "convertStrtoHex: " + sb.toString());
        return sb.toString();
    }

    public byte[] hexStringToByteArray(String s) {
        Log.d(TAG, "hexStringToByteArray: " + s);
        int len = s.length();
        byte[] data = new byte[len/2];

        for(int i = 0; i < len; i+=2){
            data[i/2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i+1), 16));
        }

        return data;
    }


    private DataOrder buildDataOrder() {
        DataOrder dataOrder = new DataOrder();
        dataOrder.setServiceName(serviceOrder);
        dataOrder.setOrderId(orderId);
        dataOrder.setDescription("");
        dataOrder.setTotalAmount(1000);

        List<DataOrder.BillInfor> billInfors = new ArrayList<>();
        billInfors.add(new DataOrder.BillInfor("item1", "1", "1000"));
        dataOrder.setBillInfor(billInfors);

        Log.d(TAG, "buildDataOrder: " + MyGson.getGson().toJson(dataOrder));
        return dataOrder;
    }

    private DataVoid buildDataVoid(String orderID, String transCode, String paymentIdentifier) {
        DataVoid dataVoid = new DataVoid();
        dataVoid.setServiceName(serviceVoidTrans);
        dataVoid.setOrderId(orderID);
        dataVoid.setTransCode(transCode);
        dataVoid.setPaymentIdentifier(paymentIdentifier);
        return dataVoid;
    }
}
