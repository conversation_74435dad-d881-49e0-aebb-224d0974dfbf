package com.xthuan.clientsocket;

import com.google.gson.annotations.Expose;

import java.util.List;

public class DataOrder {
    @Expose
    public String serviceName;
    @Expose
    public String typePayment;
    @Expose
    public String orderId;
    @Expose
    public long totalAmount;
    @Expose
    public String paymentMethod;
    @Expose
    public int autoDismissDlgTimer;
    @Expose
    public String description;
    @Expose
    public List<BillInfor> billInfor;

    public DataOrder() {
    }

    public DataOrder(String serviceName, String orderId, long totalAmount, String description, String typePayment) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.totalAmount = totalAmount;
        this.description = description;
        this.typePayment = typePayment;
    }



    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public int getAutoDismissDlgTimer() {
        return autoDismissDlgTimer;
    }

    public void setAutoDismissDlgTimer(int autoDismissDlgTimer) {
        this.autoDismissDlgTimer = autoDismissDlgTimer;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<BillInfor> getBillInfor() {
        return billInfor;
    }

    public void setBillInfor(List<BillInfor> billInfor) {
        this.billInfor = billInfor;
    }

    public String gettypePayment() {
        return typePayment;
    }

    public void settypePayment(String typePayment) {
        this.typePayment = typePayment;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    static class BillInfor{
        @Expose
        String item;
        @Expose
        String quantity;
        @Expose
        String amount;

        public BillInfor() {
        }

        public BillInfor(String item, String quantity, String amount) {
            this.item = item;
            this.quantity = quantity;
            this.amount = amount;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }
}
