package com.xthuan.clientsocket;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

public class PacketCrypto {

    /**
     * Giải mã cả gói <STX><MSGLEN><DATA><ETX><LRC> và trả về DATA gốc.
     *
     * @param packet  Mảng byte đầy đủ gói tin do buildEncryptedMessage tạo ra.
     * @param keyStr  Khóa 24 ký tự ASCII.
     * @return        Chuỗi DATA gốc (ASCII), đã trim zeros padding.
     */

    public static byte[] buildEncryptedMessage(String msg, String keyStr) throws Exception {
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv = new byte[8]; // 8 bytes zero IV
        byte[] plainData = msg.getBytes(StandardCharsets.US_ASCII);

        // Encrypt DATA
        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // Padding manually to make multiple of 8
        int padLen = 8 - (plainData.length % 8);
        byte[] paddedPlainData = Arrays.copyOf(plainData, plainData.length + padLen);

        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encryptedData = cipher.doFinal(paddedPlainData);

        // Message length
        int len = encryptedData.length;

        // Construct packet
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(0x02);                       // STX
        baos.write((len >> 8) & 0xFF);         // MSGLEN High byte
        baos.write(len & 0xFF);                // MSGLEN Low byte
        baos.write(encryptedData);             // Encrypted DATA
        baos.write(0x03);                      // ETX

        byte[] withoutLRC = baos.toByteArray();
        byte lrc = 0x00;
        for (int i = 1; i < withoutLRC.length; i++) {
            lrc ^= withoutLRC[i];              // LRC = XOR from MSGLEN to ETX
        }

        baos.write(lrc);                       // LRC
        return baos.toByteArray();
    }

    public static String decryptPacket(DataInputStream in, String keyStr) throws Exception {
//        DataInputStream in = new DataInputStream(new ByteArrayInputStream(packet));

        // 1. STX
        byte stx = in.readByte();
        if (stx != 0x02) {
            throw new IllegalArgumentException("Invalid STX: " + stx);
        }

        // 2. MSGLEN (2 bytes)
        int lenHigh = in.readUnsignedByte();
        int lenLow = in.readUnsignedByte();
        int dataLen = (lenHigh << 8) | lenLow;

        // 3. Đọc DATA đã mã hóa
        byte[] encryptedData = new byte[dataLen];
        in.readFully(encryptedData);

        // 4. ETX
        byte etx = in.readByte();
        if (etx != 0x03) {
            throw new IllegalArgumentException("Invalid ETX: " + etx);
        }

        // 5. LRC
        byte lrc = in.readByte();
        // Tính LRC
        byte calcLrc = 0x00;
        calcLrc ^= (byte) lenHigh;
        calcLrc ^= (byte) lenLow;
        for (byte b : encryptedData) {
            calcLrc ^= b;
        }
        calcLrc ^= etx;
        if (lrc != calcLrc) {
            throw new IllegalArgumentException(
                    String.format("Invalid LRC: expected 0x%02X but got 0x%02X", calcLrc, lrc)
            );
        }

        // 6. Khởi tạo Cipher DESede/CBC/NoPadding
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv  = new byte[8];

        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

        // 7. Giải mã và loại bỏ zero-padding
        byte[] decryptedPadded = cipher.doFinal(encryptedData);
        // Tìm vị trí cuối cùng không phải zero
        int trimIndex = decryptedPadded.length;
        while (trimIndex > 0 && decryptedPadded[trimIndex - 1] == 0x00) {
            trimIndex--;
        }
        byte[] decrypted = Arrays.copyOf(decryptedPadded, trimIndex);

        return new String(decrypted, StandardCharsets.US_ASCII);
    }
}
