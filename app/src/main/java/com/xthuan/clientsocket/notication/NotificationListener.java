package com.xthuan.clientsocket.notication;

import static com.xthuan.clientsocket.SocketClient.ADD_ORDER_INFOR;

import android.app.Notification;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.util.Log;

import com.xthuan.clientsocket.DataOrder;
import com.xthuan.clientsocket.SocketClient;
import com.xthuan.clientsocket.screen.MainActivity;

public class NotificationListener extends NotificationListenerService {
    Context context;

    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        String pack = sbn.getPackageName();
        String ticker ="";
        if(sbn.getNotification().tickerText !=null) {
            ticker = sbn.getNotification().tickerText.toString();
        }
        Bundle extras = sbn.getNotification().extras;
        String title = extras.getString("android.title");
        String text = extras.getCharSequence("android.text").toString();
        int id1 = extras.getInt(Notification.EXTRA_SMALL_ICON);
        Bitmap id = sbn.getNotification().largeIcon;

        Log.i("Package",pack);
        Log.i("Ticker",ticker);
        Log.i("Title",title);
        Log.i("Text",text);

        String money = text.split("/+")[1].split("VND")[0].replaceAll(",", "").trim();
        SocketClient socketClient = new SocketClient();
        socketClient.setIpServer("***********");
        socketClient.setContext(context);
        socketClient.setDataOrder(new DataOrder(ADD_ORDER_INFOR, "orrderId", Long.parseLong(money), "", ""));
        socketClient.pushPayment(ADD_ORDER_INFOR);
    }
}


//2025-01-13 16:23:48.822 28434-28434 Package                 com.xthuan.clientsocket              I  com.vietinbank.ipay
//2025-01-13 16:23:48.822 28434-28434 Ticker                  com.xthuan.clientsocket              I
//2025-01-13 16:23:48.822 28434-28434 Title                   com.xthuan.clientsocket              I  Biến động số dư
//2025-01-13 16:23:48.822 28434-28434 Text                    com.xthuan.clientsocket              I  Thời gian: 13/01/2025 16:23
//                                                                                                    Tài khoản: ************
//                                                                                                    Giao dịch: +890,000VND
//                                                                                                    Số dư hiện tại: 983,363VND
//                                                                                                    Nội dung: CT DEN:************ NGUYEN XUAN THUAN chuyen FT25013094634201; tai Napas
