package com.xthuan.clientsocket;

import com.google.gson.annotations.Expose;

import java.util.List;

public class DataMoto {
    @Expose
    public String serviceName;
    @Expose
    public String orderId;
    @Expose
    public long totalAmount;
    @Expose
    public String pan;
    @Expose
    public String cardHolder;
    @Expose
    public String cardExp;
    @Expose
    public String cvv;
    @Expose
    public String phone;
    @Expose
    public String email;

    public DataMoto(String serviceName, String orderId, long totalAmount, String pan, String cardHolder, String cardExp, String cvv, String phone, String email) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.totalAmount = totalAmount;
        this.pan = pan;
        this.cardHolder = cardHolder;
        this.cardExp = cardExp;
        this.cvv = cvv;
        this.phone = phone;
        this.email = email;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public String getCardExp() {
        return cardExp;
    }

    public void setCardExp(String cardExp) {
        this.cardExp = cardExp;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
