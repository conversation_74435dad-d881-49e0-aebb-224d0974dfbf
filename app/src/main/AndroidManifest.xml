<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.xthuan.clientsocket">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>

    <application
            android:allowBackup="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher"
            android:supportsRtl="true"
            android:theme="@style/Theme.ClientSocket"
            android:usesCleartextTraffic="true">
        <activity
                android:name=".screen.MainActivityTakashimaya"
                android:exported="false"/>
        <activity
                android:name=".screen.MainActivity2"
                android:exported="false"/>
        <activity
                android:name=".screen.HomeActivity"
                android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
                android:name=".screen.MainActivity"
                android:exported="false"/>
        <activity
                android:name=".screen.ActivityPushPayment"
                android:exported="false"/>

        <service
                android:name=".notication.NotificationListener"
                android:exported="false"
                android:label="service_noti"
                android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService"/>
            </intent-filter>

            <meta-data
                    android:name="android.service.notification.default_filter_types"
                    android:value="conversations|alerting">
            </meta-data>
            <meta-data
                    android:name="android.service.notification.disabled_filter_types"
                    android:value="ongoing|silent">
            </meta-data>
        </service>
    </application>

</manifest>