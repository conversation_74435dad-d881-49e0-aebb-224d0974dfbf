plugins {
    id 'com.android.application'
}

android {
    compileSdk 34

    defaultConfig {
        applicationId "com.xthuan.clientsocket"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {

    // AndroidX core và UI
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Activity (hạ cấp để chạy với SDK 34)
    implementation 'androidx.activity:activity:1.9.0'   // :contentReference[oaicite:5]{index=5}

    // Test
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    // GSON, HTTP
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation files('libs/android-async-http-1.4.11.jar')
    implementation files('libs/httpclient-4.5.8.jar')

    implementation 'com.google.android.material:material:1.2.0-alpha03'

}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.jetbrains.kotlin') {
            // force every kotlin artifact to 1.8.22
            details.useVersion '1.8.22'
            details.because "avoid mixing stdlib 1.8.22 with jdk7/jdk8 1.6.21"
        }
    }
}